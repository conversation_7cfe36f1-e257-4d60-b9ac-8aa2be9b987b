# دليل التدريب المتقدم للنماذج الأدبية العربية

## 🎯 **المشكلة الحالية:**
النماذج الموجودة تنتج نصوص ركيكة وضعيفة مقارنة بمستوى نجيب محفوظ وأحمد مراد.

## 🚀 **الحلول المتقدمة:**

### 1. **Fine-tuning (التدريب المخصص)**

#### **أ) إعداد مجموعة البيانات:**

```json
{
  "conversations": [
    {
      "messages": [
        {
          "role": "system",
          "content": "أنت نجيب محفوظ، اكتب بأسلوبك المميز"
        },
        {
          "role": "user", 
          "content": "اكتب فقرة عن حارة شعبية في القاهرة"
        },
        {
          "role": "assistant",
          "content": "كانت الحارة تستيقظ مع أول خيوط الفجر، حين تتسلل أشعة الشمس الذهبية عبر النوافذ الخشبية المتهالكة، لتداعب وجوه النائمين برفق الأم الحانية. في هذا الوقت المقدس من النهار، كانت أم محمود تفتح باب بيتها الصغير، وتخرج إلى الحوش لتملأ الجرة من الحنفية النحاسية التي تقف في وسط الحارة كشاهد على تاريخ طويل من الحياة المشتركة..."
        }
      ]
    }
  ]
}
```

#### **ب) مصادر البيانات المطلوبة:**

**للتدريب على أسلوب نجيب محفوظ:**
- الثلاثية (بين القصرين، قصر الشوق، السكرية)
- أولاد حارتنا
- زقاق المدق
- خان الخليلي
- بداية ونهاية

**للتدريب على أسلوب أحمد مراد:**
- تراب الماس
- الفيل الأزرق
- 1919
- أرض الإله
- موسم صيد الغزلان

### 2. **RAG (Retrieval-Augmented Generation)**

#### **إنشاء قاعدة معرفة أدبية:**

```python
# مثال على هيكل قاعدة البيانات
literary_database = {
    "naguib_mahfouz": {
        "style_characteristics": [
            "الوصف التفصيلي للأماكن والشخصيات",
            "استخدام الرمزية الاجتماعية", 
            "الحوار الطبيعي المعبر عن الطبقات",
            "التحليل النفسي العميق"
        ],
        "sample_passages": [
            {
                "context": "وصف حارة شعبية",
                "text": "كانت الحارة كالكتاب المفتوح، تحكي قصص أهلها...",
                "techniques": ["التشبيه", "الاستعارة", "الوصف الحسي"]
            }
        ],
        "vocabulary": ["الحارة", "الحوش", "المشربية", "الدكة"],
        "sentence_patterns": [
            "كان [الشخص] [يفعل] [في المكان] [في الوقت]",
            "لم يكن [الشخص] يدري أن [الحدث] سيغير [حياته]"
        ]
    }
}
```

### 3. **Prompt Engineering المتقدم**

#### **أ) نظام التوجيهات المتدرج:**

```python
def build_advanced_prompt(author_style, content_type, context):
    base_prompt = f"""
أنت {author_style}، الكاتب العربي العظيم. 

خصائص أسلوبك:
{get_style_characteristics(author_style)}

تقنياتك المميزة:
{get_writing_techniques(author_style)}

مفرداتك المفضلة:
{get_preferred_vocabulary(author_style)}

أنماط جملك:
{get_sentence_patterns(author_style)}

الآن اكتب {content_type} بأسلوبك الأصيل:
"""
    return base_prompt
```

#### **ب) أمثلة للتوجيهات المحسنة:**

**لنجيب محفوظ:**
```
أنت نجيب محفوظ. اكتب بأسلوبك المميز:

الخصائص الأساسية:
- ابدأ بوصف تفصيلي للمكان والزمان
- استخدم الرمزية لتعكس القضايا الاجتماعية
- اجعل كل شخصية تمثل طبقة أو فكرة معينة
- استخدم الحوار ليعكس مستوى التعليم والطبقة
- اربط الأحداث الصغيرة بالتغيرات الكبيرة في المجتمع

التقنيات:
- الوصف الحسي المفصل (الأصوات، الروائح، الألوان)
- التنقل بين الأزمنة لربط الماضي بالحاضر
- استخدام الأمثال والحكم الشعبية
- التحليل النفسي من خلال الأفعال والسلوكيات

المفردات المميزة:
الحارة، المشربية، الدكة، الحوش، الترام، القهوة الشعبية، الجلباب، الطربوش

أنماط الجمل:
- "كان [الاسم] رجلاً في [العمر]، [الوصف الجسدي]، [الوصف النفسي]"
- "لم يكن يدري أن هذا اليوم سيحمل له..."
- "في تلك الأيام التي..."

الآن اكتب فقرة عن: [الموضوع]
```

### 4. **تدريب نماذج محلية متخصصة**

#### **أ) استخدام Ollama للتدريب المحلي:**

```bash
# تحميل نموذج أساسي
ollama pull llama2:7b

# إعداد ملف التدريب
cat > arabic_literary_training.txt << EOF
<s>[INST] اكتب بأسلوب نجيب محفوظ عن حارة شعبية [/INST]
كانت الحارة تنبض بالحياة منذ الفجر الأول، حين تتسلل أشعة الشمس الذهبية عبر شقوق النوافذ الخشبية المتهالكة...
</s>
EOF

# بدء التدريب
ollama create arabic-mahfouz -f ./Modelfile
```

#### **ب) استخدام LoRA للتدريب السريع:**

```python
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType

# تحميل النموذج الأساسي
model = AutoModelForCausalLM.from_pretrained("microsoft/DialoGPT-medium")
tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-medium")

# إعداد LoRA
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=16,
    lora_alpha=32,
    lora_dropout=0.1,
    target_modules=["q_proj", "v_proj"]
)

# تطبيق LoRA
model = get_peft_model(model, lora_config)

# التدريب على البيانات الأدبية
train_dataset = load_arabic_literary_dataset()
trainer = Trainer(
    model=model,
    train_dataset=train_dataset,
    tokenizer=tokenizer
)
trainer.train()
```

### 5. **تحسين النماذج الموجودة**

#### **أ) إنشاء مكتبة أساليب:**

```javascript
const literaryStyles = {
  "نجيب محفوظ": {
    openingPatterns: [
      "كان {character} {description} في {place}",
      "في ذلك الوقت من {time}, كانت {place} تشهد",
      "لم يكن {character} يتوقع أن {event}"
    ],
    descriptiveElements: [
      "الوصف الحسي المفصل",
      "ربط المكان بالحالة النفسية", 
      "استخدام الرمزية الاجتماعية"
    ],
    vocabulary: {
      places: ["الحارة", "الحوش", "المشربية", "الدكة"],
      emotions: ["الحنين", "الأسى", "الترقب", "الحيرة"],
      social: ["الطبقة", "التقاليد", "التغيير", "الصراع"]
    }
  }
}
```

#### **ب) نظام التحسين التدريجي:**

```javascript
function enhanceWriting(text, targetStyle) {
  const style = literaryStyles[targetStyle];
  
  // تحليل النص الحالي
  const analysis = analyzeText(text);
  
  // تطبيق تحسينات الأسلوب
  let enhancedText = text;
  
  // إضافة الوصف التفصيلي
  enhancedText = addDetailedDescription(enhancedText, style);
  
  // تحسين المفردات
  enhancedText = enhanceVocabulary(enhancedText, style.vocabulary);
  
  // تطبيق أنماط الجمل
  enhancedText = applyPatterns(enhancedText, style.patterns);
  
  return enhancedText;
}
```

### 6. **خطة التنفيذ العملية**

#### **المرحلة الأولى (شهر واحد):**
1. جمع وتنظيف النصوص الأدبية
2. إنشاء قاعدة بيانات الأساليب
3. تطوير نظام Prompt Engineering متقدم

#### **المرحلة الثانية (شهرين):**
1. تدريب نماذج LoRA متخصصة
2. تطوير نظام RAG للأساليب الأدبية
3. اختبار وتقييم النتائج

#### **المرحلة الثالثة (شهر واحد):**
1. تحسين النماذج بناءً على التقييم
2. إنشاء واجهة للتدريب المستمر
3. نشر النماذج المحسنة

### 7. **أدوات التقييم**

#### **مقاييس الجودة الأدبية:**
```python
def evaluate_literary_quality(text, target_style):
    scores = {
        "style_consistency": measure_style_consistency(text, target_style),
        "vocabulary_richness": calculate_vocabulary_richness(text),
        "narrative_flow": assess_narrative_flow(text),
        "character_depth": evaluate_character_development(text),
        "cultural_authenticity": check_cultural_elements(text)
    }
    return scores
```

### 8. **التكاليف والموارد**

#### **التدريب المحلي:**
- GPU قوي (RTX 4090 أو أفضل)
- 32GB RAM على الأقل
- مساحة تخزين 500GB+

#### **التدريب السحابي:**
- Google Colab Pro: $10/شهر
- AWS/Azure: $50-200/شهر حسب الاستخدام
- Hugging Face Spaces: مجاني للمشاريع الصغيرة

### 9. **البدائل السريعة**

#### **أ) تحسين Prompts فوري:**
```javascript
const improvedPrompts = {
  "نجيب محفوظ": `
أنت نجيب محفوظ. اتبع هذا النمط بدقة:

1. ابدأ بوصف المكان والزمان بتفصيل دقيق
2. قدم الشخصية بوصف جسدي ونفسي مفصل
3. استخدم الحوار ليعكس الطبقة الاجتماعية
4. اربط الأحداث الصغيرة بالتغيرات الكبيرة
5. استخدم الرمزية والاستعارات

مثال على أسلوبك:
"كان أحمد عبد الجواد رجلاً في الخامسة والأربعين، طويل القامة، عريض المنكبين، أسمر اللون، كث اللحية، له عينان سوداوان واسعتان..."

الآن اكتب: [الموضوع]
  `
}
```

#### **ب) نظام التحسين التلقائي:**
```javascript
function autoEnhance(text) {
  // إضافة تفاصيل حسية
  text = addSensoryDetails(text);
  
  // تحسين الحوار
  text = enhanceDialogue(text);
  
  // إضافة عمق نفسي
  text = addPsychologicalDepth(text);
  
  return text;
}
```

## 🎯 **التوصية الفورية:**

للحصول على نتائج سريعة، ابدأ بـ:
1. **تحسين Prompts** (يمكن تطبيقه اليوم)
2. **إنشاء مكتبة أساليب** (أسبوع واحد)
3. **تدريب LoRA** (شهر واحد)

هل تريد أن نبدأ بتطبيق أي من هذه الحلول؟
