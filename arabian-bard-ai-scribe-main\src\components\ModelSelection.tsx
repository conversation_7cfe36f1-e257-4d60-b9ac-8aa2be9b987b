
import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Cpu, Database, Zap } from "lucide-react";

interface ModelSelectionProps {
  activeModel: string;
  setActiveModel: (model: string) => void;
}

export const ModelSelection = ({ activeModel, setActiveModel }: ModelSelectionProps) => {
  const models = [
    {
      id: "llama-3.1-70b",
      name: "Llama 3.3 70B",
      description: "نموذج متقدم بـ 70 مليار معامل، مثالي للأدب المعقد",
      specs: {
        parameters: "70B",
        memory: "140GB",
        performance: "عالي جداً"
      },
      features: ["دعم اللغة العربية", "إبداع أدبي متقدم", "فهم السياق العميق"],
      recommended: true
    },
    {
      id: "llama-3.1-8b",
      name: "<PERSON>lama 3.1 8B",
      description: "نموذج متوسط الحجم، مناسب للتطوير السريع",
      specs: {
        parameters: "8B",
        memory: "16GB",
        performance: "عالي"
      },
      features: ["استجابة سريعة", "متطلبات معتدلة", "جودة جيدة"],
      recommended: false
    },
    {
      id: "openrouter-mixtral",
      name: "OpenRouter Mixtral 8x7B",
      description: "نموذج هجين عبر OpenRouter API",
      specs: {
        parameters: "47B",
        memory: "94GB",
        performance: "عالي"
      },
      features: ["واجهة سحابية", "تكلفة متغيرة", "تحديثات مستمرة"],
      recommended: false
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">اختيار النموذج الأساسي</h2>
        <p className="text-slate-600">
          اختر النموذج الأنسب لمشروعك بناءً على الإمكانيات التقنية والأهداف المطلوبة
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {models.map((model) => (
          <Card 
            key={model.id} 
            className={`relative transition-all duration-300 ${
              activeModel === model.id 
                ? 'ring-2 ring-emerald-500 shadow-lg' 
                : 'hover:shadow-md'
            }`}
          >
            {model.recommended && (
              <Badge className="absolute -top-2 right-4 bg-amber-500 hover:bg-amber-600">
                موصى به
              </Badge>
            )}
            
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cpu className="w-5 h-5 text-emerald-600" />
                {model.name}
              </CardTitle>
              <CardDescription>{model.description}</CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-2 text-sm">
                <div className="text-center p-2 bg-slate-50 rounded">
                  <div className="font-semibold text-slate-800">{model.specs.parameters}</div>
                  <div className="text-slate-600">معاملات</div>
                </div>
                <div className="text-center p-2 bg-slate-50 rounded">
                  <div className="font-semibold text-slate-800">{model.specs.memory}</div>
                  <div className="text-slate-600">ذاكرة</div>
                </div>
                <div className="text-center p-2 bg-slate-50 rounded">
                  <div className="font-semibold text-slate-800">
                    <Zap className="w-4 h-4 mx-auto" />
                  </div>
                  <div className="text-slate-600">{model.specs.performance}</div>
                </div>
              </div>

              <div className="space-y-2">
                {model.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-emerald-500" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>

              <Button 
                className={`w-full ${
                  activeModel === model.id 
                    ? 'bg-emerald-600 hover:bg-emerald-700' 
                    : 'bg-slate-600 hover:bg-slate-700'
                }`}
                onClick={() => setActiveModel(model.id)}
              >
                {activeModel === model.id ? 'مُحدد حالياً' : 'اختيار هذا النموذج'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
