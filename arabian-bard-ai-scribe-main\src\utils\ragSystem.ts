// نظام RAG للاستعانة بقاعدة المعرفة في التوليد
import { knowledgeBase, searchKnowledgeBase, getTrainingExamples, getWritingPatterns } from '@/data/knowledgeBase';
import { getTrainingTextsByAuthor, getTrainingTextsByType } from '@/data/trainingTexts';

export interface RAGContext {
  relevantPassages: any[];
  writingPatterns: any[];
  trainingExamples: any[];
  vocabulary: string[];
  techniques: string[];
}

// دالة بناء السياق المحسن باستخدام RAG
export function buildRAGContext(
  author: string,
  contentType: string,
  topic: string
): RAGContext {
  // البحث عن النصوص ذات الصلة
  const relevantPassages = searchKnowledgeBase(author, mapContentTypeToCategory(contentType));
  
  // الحصول على أنماط الكتابة
  const writingPatterns = getWritingPatterns(author);
  
  // الحصول على أمثلة تدريبية
  const trainingExamples = getTrainingTextsByAuthor(author)
    .filter(text => text.type === mapContentTypeToCategory(contentType))
    .slice(0, 3);
  
  // الحصول على المفردات المميزة
  const authorData = knowledgeBase[author];
  const vocabulary = authorData ? authorData.vocabulary : [];
  const techniques = authorData ? authorData.techniques : [];
  
  return {
    relevantPassages,
    writingPatterns,
    trainingExamples,
    vocabulary,
    techniques
  };
}

// دالة تحويل نوع المحتوى إلى فئة
function mapContentTypeToCategory(contentType: string): string {
  const mapping = {
    'story': 'narrative',
    'poem': 'narrative',
    'article': 'narrative',
    'dialogue': 'dialogue',
    'description': 'place_description'
  };
  return mapping[contentType] || 'narrative';
}

// دالة بناء Prompt محسن باستخدام RAG
export function buildRAGEnhancedPrompt(
  author: string,
  contentType: string,
  topic: string,
  length: string
): string {
  const context = buildRAGContext(author, contentType, topic);
  
  const lengthInstructions = {
    'short': 'قصير (100-300 كلمة)',
    'medium': 'متوسط (300-800 كلمة)',
    'long': 'طويل (800-1500 كلمة)'
  };

  let prompt = `أنت ${author}، الكاتب العربي العظيم. اكتب بأسلوبك الأصيل والمميز.

## خصائص أسلوبك:
${context.techniques.map(t => `• ${t}`).join('\n')}

## مفرداتك المميزة:
${context.vocabulary.slice(0, 15).join(', ')}

## أنماط جملك المفضلة:`;

  // إضافة أنماط الكتابة
  if (context.writingPatterns.length > 0) {
    prompt += '\n' + context.writingPatterns.slice(0, 3).map(pattern => 
      `• ${pattern.pattern}\n  مثال: "${pattern.examples[0]}"`
    ).join('\n');
  }

  // إضافة أمثلة من أعمالك
  if (context.relevantPassages.length > 0) {
    prompt += '\n\n## مثال من أعمالك:';
    const example = context.relevantPassages[0];
    prompt += `\n"${example.text}"\n\nالتقنيات المستخدمة: ${example.techniques.join(', ')}`;
  }

  // إضافة أمثلة تدريبية
  if (context.trainingExamples.length > 0) {
    prompt += '\n\n## أمثلة للاسترشاد:';
    context.trainingExamples.slice(0, 2).forEach((example, index) => {
      prompt += `\n\n### مثال ${index + 1}:
الطلب: "${example.prompt}"
إجابتك: "${example.response.substring(0, 200)}..."
التقنيات: ${example.techniques.join(', ')}`;
    });
  }

  prompt += `\n\n## المطلوب الآن:
اكتب ${getContentTypeInArabic(contentType)} بطول ${lengthInstructions[length]} عن: ${topic}

## تعليمات مهمة:
• اتبع أسلوبك المميز بدقة
• استخدم تقنياتك الخاصة
• اعتمد على مفرداتك المميزة
• طبق أنماط جملك المفضلة
• اجعل النص أصيلاً ومعبراً
• لا تستخدم أي كلمات أجنبية مطلقاً

اكتب الآن:`;

  return prompt;
}

function getContentTypeInArabic(type: string): string {
  const types = {
    'story': 'قصة',
    'poem': 'قصيدة',
    'article': 'مقال',
    'dialogue': 'حوار',
    'description': 'وصف'
  };
  return types[type] || 'نص';
}

// دالة تحليل النص المولد مقارنة بقاعدة المعرفة
export function analyzeGeneratedText(
  text: string,
  author: string,
  contentType: string
): {
  score: number;
  feedback: string[];
  suggestions: string[];
  matchedTechniques: string[];
  matchedVocabulary: string[];
} {
  const context = buildRAGContext(author, contentType, '');
  const feedback: string[] = [];
  const suggestions: string[] = [];
  const matchedTechniques: string[] = [];
  const matchedVocabulary: string[] = [];
  let score = 10;

  // تحليل استخدام المفردات المميزة
  const usedVocabulary = context.vocabulary.filter(word => 
    text.includes(word)
  );
  matchedVocabulary.push(...usedVocabulary);

  if (usedVocabulary.length === 0) {
    score -= 3;
    feedback.push("لا يستخدم المفردات المميزة للكاتب");
    suggestions.push(`استخدم كلمات مثل: ${context.vocabulary.slice(0, 5).join(', ')}`);
  } else if (usedVocabulary.length < 3) {
    score -= 1;
    feedback.push("استخدام قليل للمفردات المميزة");
    suggestions.push("أضف المزيد من المفردات المميزة للكاتب");
  }

  // تحليل التقنيات المستخدمة
  const authorData = knowledgeBase[author];
  if (authorData) {
    // فحص تقنيات نجيب محفوظ
    if (author === "نجيب محفوظ") {
      if (text.includes("كان") && text.includes("رجلاً في")) {
        matchedTechniques.push("وصف الشخصيات بالنمط المحفوظي");
        score += 1;
      }
      if (text.includes("الحارة") || text.includes("الحوش")) {
        matchedTechniques.push("استخدام الأماكن التراثية");
        score += 1;
      }
      if (text.match(/في ذلك الوقت|في تلك الأيام/)) {
        matchedTechniques.push("ربط الزمان بالمكان");
        score += 1;
      }
    }

    // فحص تقنيات أحمد مراد
    if (author === "أحمد مراد") {
      if (text.includes("فتح عينيه فجأة") || text.includes("فجأة")) {
        matchedTechniques.push("البداية المشوقة");
        score += 1;
      }
      if (text.includes("لم يكن يتوقع")) {
        matchedTechniques.push("خلق المفاجأة");
        score += 1;
      }
      if (text.match(/التوتر|القلق|الخوف|الإثارة/)) {
        matchedTechniques.push("خلق الأجواء المتوترة");
        score += 1;
      }
    }
  }

  // تحليل طول النص
  const wordCount = text.split(' ').length;
  if (wordCount < 50) {
    score -= 2;
    feedback.push("النص قصير جداً");
    suggestions.push("أضف المزيد من التفاصيل والوصف");
  }

  // تحليل جودة اللغة
  const foreignWords = text.match(/[a-zA-Z]{3,}/g);
  if (foreignWords && foreignWords.length > 0) {
    score -= 3;
    feedback.push(`يحتوي على كلمات أجنبية: ${foreignWords.join(', ')}`);
    suggestions.push("استخدم اللغة العربية فقط");
  }

  // تحليل بنية الجمل
  const sentences = text.split(/[.!?]/).filter(s => s.trim().length > 0);
  if (sentences.length < 3) {
    score -= 1;
    feedback.push("عدد الجمل قليل");
    suggestions.push("اكتب جمل أكثر تنوعاً");
  }

  return {
    score: Math.max(0, Math.min(10, score)),
    feedback,
    suggestions,
    matchedTechniques,
    matchedVocabulary
  };
}

// دالة الحصول على اقتراحات للتحسين
export function getSuggestions(author: string, contentType: string): string[] {
  const context = buildRAGContext(author, contentType, '');
  const suggestions: string[] = [];

  if (author === "نجيب محفوظ") {
    suggestions.push(
      "ابدأ بوصف تفصيلي للمكان والزمان",
      "قدم الشخصيات بوصف جسدي ونفسي مفصل",
      "استخدم الحوار ليعكس الطبقة الاجتماعية",
      "اربط الأحداث الصغيرة بالتغيرات الكبيرة"
    );
  }

  if (author === "أحمد مراد") {
    suggestions.push(
      "ابدأ بمشهد مشوق يجذب القارئ",
      "اخلق جو من التوتر والإثارة",
      "استخدم حوار سريع وحاد",
      "أضف عنصر الغموض والكشف التدريجي"
    );
  }

  // إضافة اقتراحات من المفردات المميزة
  if (context.vocabulary.length > 0) {
    suggestions.push(`استخدم مفردات مثل: ${context.vocabulary.slice(0, 5).join(', ')}`);
  }

  return suggestions;
}
