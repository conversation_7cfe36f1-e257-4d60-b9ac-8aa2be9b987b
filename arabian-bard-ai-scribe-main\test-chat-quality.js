// اختبار جودة الإجابات والاتساق في الشات بوت
const fs = require('fs');

// قراءة مفاتيح API من ملف البيئة
const envContent = fs.readFileSync('.env.local', 'utf8');
const GROQ_API_KEY = envContent.match(/VITE_GROQ_API_KEY=(.+)/)?.[1];

// أسئلة اختبار لفحص جودة الإجابات
const testQuestions = [
  {
    question: "مرحباً",
    expectedType: "greeting",
    description: "تحية بسيطة - يجب أن يرد بتحية مهذبة"
  },
  {
    question: "أريد كتابة قصة قصيرة عن البحر",
    expectedType: "writing_request",
    description: "طلب كتابة - يجب أن يساعد في الكتابة أو يطلب تفاصيل"
  },
  {
    question: "ما هو أسلوب نجيب محفوظ؟",
    expectedType: "information",
    description: "سؤال معلوماتي - يجب أن يشرح أسلوب نجيب محفوظ"
  },
  {
    question: "اكتب لي بيت شعر عن الوطن",
    expectedType: "poetry",
    description: "طلب شعر - يجب أن يكتب بيت شعر باللغة العربية"
  }
];

const models = [
  {
    name: 'Mistral Saba 24B',
    model: 'mistral-saba-24b',
    apiKey: GROQ_API_KEY,
    url: 'https://api.groq.com/openai/v1/chat/completions'
  }
];

async function testChatQuality(modelConfig, question) {
  console.log(`\n🔍 اختبار: "${question.question}"`);
  console.log(`📝 المتوقع: ${question.description}`);
  
  try {
    const response = await fetch(modelConfig.url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${modelConfig.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: modelConfig.model,
        messages: [
          {
            role: 'system',
            content: `أنت مساعد ذكي متخصص في الكتابة الأدبية العربية. أنت خبير في الأدب العربي ومتمكن من أساليب الكتاب العرب العظماء.

قواعد أساسية يجب اتباعها بدقة:
1. اكتب باللغة العربية الفصحى فقط - لا تستخدم أي كلمات أجنبية
2. اقرأ رسالة المستخدم بعناية وأجب عليها مباشرة
3. كن متسقاً في أسلوبك ولا تتناقض مع نفسك
4. إذا سأل المستخدم سؤالاً، أجب عليه بوضوح ودون إضافات غير مطلوبة
5. إذا طلب كتابة نص، اكتب النص المطلوب فقط
6. لا تخترع خيارات أو اقتراحات لم يطلبها المستخدم
7. كن مفيداً ومباشراً ومفهوماً في إجاباتك
8. حافظ على السياق والاستمرارية في المحادثة

مهمتك: فهم طلب المستخدم بدقة والإجابة عليه بوضوح ومهنية.`
          },
          {
            role: 'user',
            content: question.question
          }
        ],
        max_tokens: 300,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ خطأ HTTP ${response.status}: ${errorText}`);
      return { success: false, error: `HTTP ${response.status}` };
    }

    const data = await response.json();
    const answer = data.choices?.[0]?.message?.content;
    
    if (answer) {
      console.log(`✅ الإجابة: ${answer}`);
      
      // تحليل جودة الإجابة
      const quality = analyzeAnswerQuality(answer, question);
      console.log(`📊 تقييم الجودة: ${quality.score}/10`);
      
      if (quality.issues.length > 0) {
        console.log(`⚠️  مشاكل محتملة:`);
        quality.issues.forEach(issue => console.log(`   - ${issue}`));
      }
      
      return { 
        success: true, 
        answer: answer,
        quality: quality,
        question: question.question
      };
    } else {
      console.log(`❌ لا توجد إجابة صالحة`);
      return { success: false, error: 'لا توجد إجابة' };
    }
    
  } catch (error) {
    console.log(`❌ خطأ في الاتصال: ${error.message}`);
    return { success: false, error: error.message };
  }
}

function analyzeAnswerQuality(answer, question) {
  let score = 10;
  const issues = [];
  
  // فحص اللغة العربية
  const arabicRegex = /[\u0600-\u06FF]/;
  if (!arabicRegex.test(answer)) {
    score -= 5;
    issues.push('لا يحتوي على نص عربي');
  }
  
  // فحص وجود كلمات أجنبية
  const foreignWords = answer.match(/[a-zA-Z]{3,}/g);
  if (foreignWords && foreignWords.length > 2) {
    score -= 3;
    issues.push(`يحتوي على كلمات أجنبية: ${foreignWords.slice(0, 3).join(', ')}`);
  }
  
  // فحص طول الإجابة
  if (answer.length < 20) {
    score -= 2;
    issues.push('الإجابة قصيرة جداً');
  } else if (answer.length > 1000) {
    score -= 1;
    issues.push('الإجابة طويلة جداً');
  }
  
  // فحص الاتساق حسب نوع السؤال
  switch (question.expectedType) {
    case 'greeting':
      if (!answer.includes('مرحب') && !answer.includes('أهلا') && !answer.includes('السلام')) {
        score -= 2;
        issues.push('لا يحتوي على تحية مناسبة');
      }
      break;
      
    case 'poetry':
      if (!answer.includes('شعر') && !answer.includes('بيت') && answer.split('\n').length < 2) {
        score -= 3;
        issues.push('لا يبدو كشعر أو لا يحتوي على أبيات');
      }
      break;
      
    case 'writing_request':
      if (!answer.includes('قصة') && !answer.includes('كتابة') && !answer.includes('سرد')) {
        score -= 2;
        issues.push('لا يتعامل مع طلب الكتابة بشكل مناسب');
      }
      break;
  }
  
  // فحص وجود خيارات غير مطلوبة
  if (answer.includes('الخيار') || answer.includes('اختر') || answer.includes('1.') || answer.includes('2.')) {
    score -= 3;
    issues.push('يحتوي على خيارات لم يطلبها المستخدم');
  }
  
  return {
    score: Math.max(0, score),
    issues: issues
  };
}

async function runQualityTest() {
  console.log('🚀 بدء اختبار جودة الشات بوت...\n');
  
  const model = models[0]; // Mistral Saba
  const results = [];
  
  for (const question of testQuestions) {
    const result = await testChatQuality(model, question);
    results.push(result);
    
    // انتظار قصير بين الأسئلة
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // تلخيص النتائج
  console.log('\n📊 ملخص نتائج اختبار الجودة:');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ نجح: ${successful.length}/${testQuestions.length} اختبار`);
  console.log(`❌ فشل: ${failed.length}/${testQuestions.length} اختبار\n`);
  
  if (successful.length > 0) {
    const avgScore = successful.reduce((sum, r) => sum + (r.quality?.score || 0), 0) / successful.length;
    console.log(`📈 متوسط درجة الجودة: ${avgScore.toFixed(1)}/10\n`);
    
    console.log('تفاصيل النتائج:');
    successful.forEach((result, index) => {
      console.log(`${index + 1}. السؤال: "${result.question}"`);
      console.log(`   الدرجة: ${result.quality.score}/10`);
      if (result.quality.issues.length > 0) {
        console.log(`   المشاكل: ${result.quality.issues.join(', ')}`);
      }
      console.log('');
    });
  }
  
  if (failed.length > 0) {
    console.log('الاختبارات الفاشلة:');
    failed.forEach((result, index) => {
      console.log(`${index + 1}. خطأ: ${result.error}`);
    });
  }
  
  // توصيات
  console.log('\n💡 التوصيات:');
  if (successful.length === testQuestions.length) {
    const avgScore = successful.reduce((sum, r) => sum + r.quality.score, 0) / successful.length;
    if (avgScore >= 8) {
      console.log('  🎉 ممتاز! الشات بوت يعمل بجودة عالية');
    } else if (avgScore >= 6) {
      console.log('  👍 جيد، لكن يحتاج تحسينات طفيفة');
    } else {
      console.log('  ⚠️  يحتاج تحسينات كبيرة في التوجيهات');
    }
  } else {
    console.log('  🔧 تحقق من مفاتيح API والاتصال');
  }
}

// تشغيل الاختبار
runQualityTest().catch(console.error);
