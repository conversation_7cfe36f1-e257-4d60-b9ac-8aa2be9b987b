# Supabase Configuration
VITE_SUPABASE_URL=https://xpbhzuzbnwpvlpgrcijf.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwYmh6dXpibnBwdmxwZ3JjaWpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI3MzE0OTksImV4cCI6MjA0ODMwNzQ5OX0.TgE7YZo7R5PkwmCzgxq1ssMxvGMT1KlZTNnmIQY7ZLI

# Supabase Edge Functions Environment Variables
# These should be set in Supabase Dashboard -> Settings -> Edge Functions -> Environment Variables

# OpenAI API Key (for GPT models)
OPENAI_API_KEY=sk-your-openai-api-key

# Groq API Key (for Llama models)
GROQ_API_KEY=gsk_your-groq-api-key

# Anthropic API Key (for Claude models)
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key

# Supabase Service Role Key (for Edge Functions)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Supabase URL (for Edge Functions)
SUPABASE_URL=https://your-project.supabase.co
