# Supabase Configuration
VITE_SUPABASE_URL=https://xpbhzuzbnwpvlpgrcijf.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwYmh6dXpibnBwdmxwZ3JjaWpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI3MzE0OTksImV4cCI6MjA0ODMwNzQ5OX0.TgE7YZo7R5PkwmCzgxq1ssMxvGMT1KlZTNnmIQY7ZLI

# Supabase Edge Functions Environment Variables
# These should be set in Supabase Dashboard -> Settings -> Edge Functions -> Environment Variables

# OpenAI API Key (for GPT models)
OPENAI_API_KEY=sk-your-openai-api-key

# Groq API Key (for Llama models)
GROQ_API_KEY=gsk_your-groq-api-key

# 🔑 مفاتيح API للدردشة الطبيعية (Frontend)
# احصل على مفتاح Groq مجاني من: https://console.groq.com/keys
VITE_GROQ_API_KEY=your_groq_api_key_here

# مفاتيح إضافية (اختيارية)
VITE_OPENAI_API_KEY=your_openai_api_key_here
VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ملاحظات:
# 1. انسخ هذا الملف إلى .env
# 2. أضف مفتاح Groq للحصول على ردود حقيقية
# 3. بدون مفاتيح API، سيعمل النظام في وضع المحاكاة

# Anthropic API Key (for Claude models)
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key

# Supabase Service Role Key (for Edge Functions)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Supabase URL (for Edge Functions)
SUPABASE_URL=https://your-project.supabase.co
