// مكتبة الأساليب الأدبية المتقدمة
export interface LiteraryStyle {
  name: string;
  characteristics: string[];
  techniques: string[];
  vocabulary: {
    places: string[];
    emotions: string[];
    social: string[];
    descriptive: string[];
  };
  patterns: {
    opening: string[];
    character: string[];
    dialogue: string[];
    description: string[];
  };
  examples: {
    context: string;
    text: string;
    analysis: string[];
  }[];
}

export const literaryStyles: Record<string, LiteraryStyle> = {
  "نجيب محفوظ": {
    name: "نجيب محفوظ",
    characteristics: [
      "الواقعية الاجتماعية المفصلة",
      "التحليل النفسي العميق للشخصيات",
      "الوصف التفصيلي للأماكن والأزمنة",
      "استخدام الرمزية لتعكس القضايا الكبيرة",
      "الحوار الطبيعي المعبر عن الطبقات",
      "ربط الأحداث الشخصية بالتغيرات الاجتماعية"
    ],
    techniques: [
      "البداية بوصف المكان والزمان",
      "تقديم الشخصيات بوصف جسدي ونفسي مفصل",
      "استخدام الحوار الداخلي والخارجي",
      "التنقل بين الأزمنة لربط الماضي بالحاضر",
      "استخدام الأمثال والحكم الشعبية",
      "الوصف الحسي (الأصوات، الروائح، الألوان)"
    ],
    vocabulary: {
      places: ["الحارة", "الحوش", "المشربية", "الدكة", "الترام", "القهوة الشعبية", "الزقاق", "الحي الشعبي"],
      emotions: ["الحنين", "الأسى", "الترقب", "الحيرة", "الخوف", "الأمل", "اليأس", "الفرح المكتوم"],
      social: ["الطبقة", "التقاليد", "التغيير", "الصراع", "الكرامة", "الشرف", "العادات", "المجتمع"],
      descriptive: ["متهالك", "عتيق", "مهيب", "وقور", "متواضع", "بسيط", "أصيل", "عريق"]
    },
    patterns: {
      opening: [
        "كان {character} رجلاً في {age}، {physical_description}، {personality_description}",
        "في ذلك الوقت من {time}، كانت {place} تشهد {event}",
        "لم يكن {character} يتوقع أن {event} سيغير مجرى حياته",
        "استيقظت {place} على {sound/event} كما تستيقظ كل يوم"
      ],
      character: [
        "{name} {age_description}، {physical_traits}، {personality_traits}",
        "كان في عينيه {emotion} يعكس {inner_state}",
        "تحدث بصوت {voice_quality} يدل على {background/education}"
      ],
      dialogue: [
        "قال بصوت {tone}: '{speech}'",
        "همس في أذنه: '{whisper}'",
        "صرخ غاضباً: '{angry_speech}'"
      ],
      description: [
        "كانت {place} تنبض بـ {activity/life}",
        "تسللت {light/sound/smell} عبر {opening/space}",
        "امتلأ المكان بـ {atmosphere/feeling}"
      ]
    },
    examples: [
      {
        context: "وصف شخصية رئيسية",
        text: "كان أحمد عبد الجواد رجلاً في الخامسة والأربعين، طويل القامة، عريض المنكبين، أسمر اللون، كث اللحية، له عينان سوداوان واسعتان، وأنف أقنى، وفم واسع تبدو منه أسنان بيضاء قوية.",
        analysis: ["وصف جسدي مفصل", "تحديد العمر بدقة", "استخدام صفات محددة", "بناء صورة بصرية واضحة"]
      },
      {
        context: "وصف مكان",
        text: "كانت الحارة تستيقظ مع أول خيوط الفجر، حين تتسلل أشعة الشمس الذهبية عبر النوافذ الخشبية المتهالكة، لتداعب وجوه النائمين برفق الأم الحانية.",
        analysis: ["ربط الوقت بالمكان", "استخدام الاستعارة", "وصف حسي", "إضفاء الحياة على الجماد"]
      }
    ]
  },

  "أحمد مراد": {
    name: "أحمد مراد",
    characteristics: [
      "السرد السينمائي المشوق",
      "التشويق والإثارة المتصاعدة",
      "الحبكة المعقدة والمتشابكة",
      "الشخصيات المركبة متعددة الأبعاد",
      "اللغة العصرية المختلطة",
      "الغموض والكشف التدريجي"
    ],
    techniques: [
      "البداية المشوقة التي تجذب القارئ",
      "التنقل بين الأزمنة والأماكن بسلاسة",
      "استخدام الفلاش باك والفلاش فورورد",
      "الحوار السريع والحاد",
      "وصف الأجواء المشحونة",
      "استخدام التفاصيل التقنية المعاصرة"
    ],
    vocabulary: {
      places: ["الشارع", "المقهى", "العمارة", "الشقة", "المكتب", "الكافيه", "الكلوب", "الفيلا"],
      emotions: ["التوتر", "القلق", "الخوف", "الإثارة", "الغضب", "الحماس", "الشك", "الترقب"],
      social: ["الفساد", "السلطة", "المال", "النفوذ", "الخداع", "المؤامرة", "الصراع", "الانتقام"],
      descriptive: ["مشحون", "متوتر", "غامض", "مريب", "خطير", "مثير", "معقد", "ملتوي"]
    },
    patterns: {
      opening: [
        "فتح عينيه فجأة. {description_of_situation}",
        "لم يكن يتوقع أن {event} سيحدث بهذه السرعة",
        "رن الهاتف في {time}. كان {character} {doing_what}",
        "كان كل شيء يبدو طبيعياً حتى {turning_point}"
      ],
      character: [
        "{name} {age}، {profession}، {distinctive_feature}",
        "نظر إليه بعينين {emotion} تخفيان {secret}",
        "كان صوته {voice_quality} يخفي {hidden_emotion}"
      ],
      dialogue: [
        "قال بحدة: '{sharp_speech}'",
        "همس بصوت مرتجف: '{nervous_speech}'",
        "صرخ في وجهه: '{angry_confrontation}'"
      ],
      description: [
        "كان الجو مشحوناً بـ {tension_type}",
        "تسلل {feeling/atmosphere} إلى {place}",
        "امتلأ المكان بـ {suspense/mystery}"
      ]
    },
    examples: [
      {
        context: "بداية مشوقة",
        text: "فتح عينيه فجأة. الظلام يلف المكان، والصمت مطبق إلا من صوت تكييف الهواء الذي يعمل بانتظام. نظر إلى ساعة يده المضيئة: الثالثة والنصف فجراً. شيء ما أيقظه، لكنه لا يستطيع تحديد ماهيته.",
        analysis: ["بداية مباشرة ومشوقة", "وصف الأجواء", "خلق التوتر", "إثارة الفضول"]
      },
      {
        context: "حوار متوتر",
        text: "قال بصوت مرتجف: 'لم أكن أعرف أن الأمر سيصل إلى هذا الحد.' نظر إليه الرجل بعينين باردتين: 'الآن تعرف. والسؤال هو: ماذا ستفعل حيال ذلك؟'",
        analysis: ["حوار يكشف التوتر", "وصف الحالة النفسية", "تصاعد الصراع", "إثارة التساؤلات"]
      }
    ]
  }
};

// دالة لبناء prompt متقدم
export function buildAdvancedPrompt(
  authorStyle: string,
  contentType: string,
  topic: string,
  length: 'short' | 'medium' | 'long' = 'medium'
): string {
  const style = literaryStyles[authorStyle];
  if (!style) {
    return `اكتب ${contentType} عن ${topic}`;
  }

  const lengthInstructions = {
    short: "اكتب فقرة واحدة (100-200 كلمة)",
    medium: "اكتب نص متوسط (300-500 كلمة)",
    long: "اكتب نص مفصل (500-800 كلمة)"
  };

  return `أنت ${style.name}، الكاتب العربي العظيم. اكتب بأسلوبك الأصيل والمميز.

خصائص أسلوبك الأساسية:
${style.characteristics.map(c => `• ${c}`).join('\n')}

تقنياتك المميزة:
${style.techniques.map(t => `• ${t}`).join('\n')}

استخدم هذه المفردات المميزة:
• الأماكن: ${style.vocabulary.places.join(', ')}
• المشاعر: ${style.vocabulary.emotions.join(', ')}
• الوصف: ${style.vocabulary.descriptive.join(', ')}

أنماط الجمل المفضلة لديك:
${style.patterns.opening.slice(0, 2).map(p => `• ${p}`).join('\n')}

مثال على أسلوبك:
"${style.examples[0].text}"

التحليل: ${style.examples[0].analysis.join(', ')}

المطلوب:
${lengthInstructions[length]} بأسلوب ${style.name} عن: ${topic}

تذكر:
• ابدأ بأسلوبك المميز
• استخدم تقنياتك الخاصة
• اجعل النص أصيلاً ومعبراً
• احرص على جمال اللغة وقوة التعبير

اكتب الآن:`;
}

// دالة لتحليل جودة النص
export function analyzeTextQuality(text: string, targetStyle: string): {
  score: number;
  feedback: string[];
  suggestions: string[];
} {
  const style = literaryStyles[targetStyle];
  const feedback: string[] = [];
  const suggestions: string[] = [];
  let score = 10;

  // تحليل طول النص
  const wordCount = text.split(' ').length;
  if (wordCount < 50) {
    score -= 2;
    feedback.push("النص قصير جداً");
    suggestions.push("أضف المزيد من التفاصيل والوصف");
  }

  // تحليل استخدام المفردات المميزة
  const styleWords = [
    ...style.vocabulary.places,
    ...style.vocabulary.emotions,
    ...style.vocabulary.descriptive
  ];
  
  const usedStyleWords = styleWords.filter(word => text.includes(word));
  if (usedStyleWords.length === 0) {
    score -= 3;
    feedback.push("لا يستخدم المفردات المميزة للكاتب");
    suggestions.push(`استخدم كلمات مثل: ${styleWords.slice(0, 5).join(', ')}`);
  }

  // تحليل بنية الجمل
  const sentences = text.split(/[.!?]/).filter(s => s.trim().length > 0);
  if (sentences.length < 3) {
    score -= 1;
    feedback.push("عدد الجمل قليل");
    suggestions.push("اكتب جمل أكثر تنوعاً");
  }

  // تحليل الوصف
  const descriptiveWords = ['كان', 'كانت', 'يبدو', 'تبدو', 'يظهر', 'تظهر'];
  const hasDescription = descriptiveWords.some(word => text.includes(word));
  if (!hasDescription) {
    score -= 2;
    feedback.push("يفتقر للوصف التفصيلي");
    suggestions.push("أضف المزيد من الوصف للشخصيات والأماكن");
  }

  return {
    score: Math.max(0, score),
    feedback,
    suggestions
  };
}
