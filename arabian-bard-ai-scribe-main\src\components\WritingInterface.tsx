
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { PenTool, Sparkles, BookOpen, Copy, Download, Loader2, Star, AlertCircle } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { createClient } from '@supabase/supabase-js';
import { WritingTips } from "@/components/WritingTips";
import { buildRAGEnhancedPrompt, analyzeGeneratedText } from "@/utils/ragSystem";

interface WritingInterfaceProps {
  activeModel: string;
}

const supabase = createClient(
  'https://xpbhzuzbnwpvlpgrcijf.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwYmh6dXpibnBwdmxwZ3JjaWpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI3MzE0OTksImV4cCI6MjA0ODMwNzQ5OX0.TgE7YZo7R5PkwmCzgxq1ssMxvGMT1KlZTNnmIQY7ZLI'
);

export const WritingInterface = ({ activeModel }: WritingInterfaceProps) => {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState("");
  const [generatedText, setGeneratedText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [writingStyle, setWritingStyle] = useState("نجيب محفوظ");
  const [textLength, setTextLength] = useState([500]);
  const [contentType, setContentType] = useState("story");
  const [context, setContext] = useState("");

  const writingStyles = [
    "نجيب محفوظ",
    "أحمد مراد",
    "غسان كنفاني",
    "أحلام مستغانمي",
    "طه حسين"
  ];

  const contentTypes = [
    { value: "story", label: "قصة" },
    { value: "poem", label: "شعر" },
    { value: "article", label: "مقال أدبي" },
    { value: "dialogue", label: "حوار" },
    { value: "description", label: "وصف" }
  ];

  const samplePrompts = [
    "اكتب فصلاً من رواية تحكي عن شاب يعيش في القاهرة ويواجه تحديات الحياة الحديثة",
    "اكتب قصيدة عن جمال الطبيعة في الربيع باللغة العربية الفصحى",
    "اكتب مونولوجاً داخلياً لشخصية تتذكر طفولتها في قرية صغيرة",
    "اكتب حواراً بين جيلين مختلفين حول التقاليد والحداثة"
  ];

  const getLength = () => {
    const length = textLength[0];
    if (length <= 200) return 'short';
    if (length <= 600) return 'medium';
    return 'long';
  };

  // دالة لتوليد المحتوى مباشرة عبر Groq API
  const generateContentDirectly = async (params: any) => {
    const groqApiKey = import.meta.env.VITE_GROQ_API_KEY;

    if (!groqApiKey) {
      throw new Error('مفتاح Groq API غير موجود');
    }

    // استخدام نظام RAG المتقدم مع قاعدة المعرفة
    const systemPrompt = buildRAGEnhancedPrompt(
      params.writingStyle,
      params.contentType,
      params.prompt,
      params.length
    );

    const userPrompt = `اكتب ${getContentTypeInArabic(params.contentType)} عن: ${params.prompt}${params.context ? `\n\nالسياق الإضافي: ${params.context}` : ''}`;

    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${groqApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama-3.3-70b-versatile',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: params.length === 'short' ? 500 : params.length === 'medium' ? 1000 : 1500,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`خطأ في Groq API: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  };

  const getContentTypeInArabic = (type: string) => {
    const types = {
      'story': 'قصة',
      'poem': 'قصيدة',
      'article': 'مقال',
      'dialogue': 'حوار',
      'description': 'وصف'
    };
    return types[type] || 'نص';
  };

  // دالة بناء التوجيهات المتقدمة للأساليب الأدبية
  const buildAdvancedLiteraryPrompt = (writingStyle: string, contentType: string, length: string) => {
    const lengthInstructions = {
      'short': 'قصير (100-300 كلمة)',
      'medium': 'متوسط (300-800 كلمة)',
      'long': 'طويل (800-1500 كلمة)'
    };

    const stylePrompts = {
      'نجيب محفوظ': `أنت نجيب محفوظ، الكاتب العربي العظيم. اكتب بأسلوبك الأصيل والمميز.

خصائص أسلوبك الأساسية:
• الواقعية الاجتماعية المفصلة - صور الحياة اليومية بتفاصيلها الدقيقة
• التحليل النفسي العميق للشخصيات - اغوص في أعماق الشخصيات ودوافعها
• الوصف التفصيلي للأماكن والأزمنة - صف الحارات والبيوت والوجوه بدقة متناهية
• استخدام الرمزية لتعكس القضايا الكبيرة - اجعل الأحداث رموزاً لقضايا أكبر
• الحوار الطبيعي المعبر عن الطبقات - استخدم لغة تعكس مستوى التعليم والطبقة
• ربط الأحداث الشخصية بالتغيرات الاجتماعية

تقنياتك المميزة:
• ابدأ بوصف المكان والزمان بتفصيل دقيق
• قدم الشخصيات بوصف جسدي ونفسي مفصل
• استخدم الحوار الداخلي والخارجي
• انتقل بين الأزمنة لربط الماضي بالحاضر
• استخدم الأمثال والحكم الشعبية
• اكتب بوصف حسي (الأصوات، الروائح، الألوان)

مفرداتك المميزة:
الحارة، الحوش، المشربية، الدكة، الترام، القهوة الشعبية، الزقاق، الحي الشعبي، الحنين، الأسى، الترقب، الحيرة

أنماط جملك المفضلة:
• "كان [الاسم] رجلاً في [العمر]، [الوصف الجسدي]، [الوصف النفسي]"
• "في ذلك الوقت من [الزمن]، كانت [المكان] تشهد [الحدث]"
• "لم يكن [الشخص] يتوقع أن [الحدث] سيغير مجرى حياته"

مثال على أسلوبك:
"كان أحمد عبد الجواد رجلاً في الخامسة والأربعين، طويل القامة، عريض المنكبين، أسمر اللون، كث اللحية، له عينان سوداوان واسعتان، وأنف أقنى، وفم واسع تبدو منه أسنان بيضاء قوية."`,

      'أحمد مراد': `أنت أحمد مراد، الكاتب المصري المعاصر. اكتب بأسلوبك المشوق والمثير.

خصائص أسلوبك الأساسية:
• السرد السينمائي المشوق - اكتب كأنك تصور فيلماً بمشاهد متتالية
• التشويق والإثارة المتصاعدة - ابن التوتر تدريجياً واتركه يتصاعد
• الحبكة المعقدة والمتشابكة - اربط الأحداث بخيوط متشابكة تنكشف تدريجياً
• الشخصيات المركبة متعددة الأبعاد - اجعل كل شخصية لها جوانب متعددة
• اللغة العصرية المختلطة - استخدم لغة الشارع المصري المعاصر مع الفصحى
• الغموض والكشف التدريجي - اخف المعلومات ثم اكشفها في اللحظة المناسبة

تقنياتك المميزة:
• ابدأ بمشهد مشوق يجذب القارئ فوراً
• انتقل بين الأزمنة والأماكن بسلاسة
• استخدم الفلاش باك والفلاش فورورد
• اكتب حوار سريع وحاد يدفع الأحداث
• صف الأجواء المشحونة والمواقف المتوترة
• استخدم التفاصيل التقنية المعاصرة

مفرداتك المميزة:
الشارع، المقهى، العمارة، الشقة، المكتب، الكافيه، التوتر، القلق، الخوف، الإثارة، الفساد، السلطة، المؤامرة

أنماط جملك المفضلة:
• "فتح عينيه فجأة. [وصف الموقف]"
• "لم يكن يتوقع أن [الحدث] سيحدث بهذه السرعة"
• "رن الهاتف في [الوقت]. كان [الشخص] [يفعل ماذا]"

مثال على أسلوبك:
"فتح عينيه فجأة. الظلام يلف المكان، والصمت مطبق إلا من صوت تكييف الهواء الذي يعمل بانتظام. نظر إلى ساعة يده المضيئة: الثالثة والنصف فجراً. شيء ما أيقظه، لكنه لا يستطيع تحديد ماهيته."`
    };

    const defaultPrompt = `أنت كاتب محترف متخصص في الأدب العربي. اكتب باللغة العربية الفصحى فقط.

قواعد مهمة:
- اكتب باللغة العربية الفصحى فقط
- لا تستخدم أي كلمات أجنبية مطلقاً
- التزم بالأسلوب المطلوب
- اكتب النص المطلوب مباشرة دون تعليقات`;

    const selectedPrompt = stylePrompts[writingStyle] || defaultPrompt;

    return `${selectedPrompt}

المطلوب:
اكتب ${getContentTypeInArabic(contentType)} بطول ${lengthInstructions[length]}

تذكر:
• ابدأ بأسلوبك المميز
• استخدم تقنياتك الخاصة
• اجعل النص أصيلاً ومعبراً
• احرص على جمال اللغة وقوة التعبير
• لا تستخدم أي كلمات أجنبية مطلقاً

اكتب الآن:`;
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setGeneratedText("");

    try {
      console.log('Generating content with:', {
        prompt,
        contentType,
        writingStyle,
        activeModel,
        length: getLength(),
        context
      });

      // استخدام Groq API مباشرة كحل بديل
      const content = await generateContentDirectly({
        prompt,
        contentType,
        writingStyle,
        activeModel,
        length: getLength(),
        context: context.trim() || undefined
      });

      console.log('Content generated:', content);
      setGeneratedText(content);

      toast({
        title: "تم إنتاج المحتوى بنجاح",
        description: `تم إنتاج ${contentTypes.find(t => t.value === contentType)?.label} بأسلوب ${writingStyle}`,
      });

    } catch (error) {
      console.error('Error generating content:', error);
      toast({
        title: "خطأ في إنتاج المحتوى",
        description: "حدث خطأ أثناء إنتاج المحتوى. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });

      // رسالة خطأ للمستخدم
      setGeneratedText('عذراً، حدث خطأ في إنتاج المحتوى. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = () => {
    if (generatedText) {
      navigator.clipboard.writeText(generatedText);
      toast({
        title: "تم النسخ",
        description: "تم نسخ النص إلى الحافظة",
      });
    }
  };

  const downloadText = () => {
    if (generatedText) {
      const blob = new Blob([generatedText], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `نص-${contentType}-${new Date().toLocaleDateString('ar')}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">واجهة الكتابة الإبداعية</h2>
        <p className="text-slate-600">
          استخدم قوة الذكاء الاصطناعي لإنتاج نصوص أدبية إبداعية بأساليب مختلفة
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PenTool className="w-5 h-5 text-blue-600" />
              منطقة الكتابة
            </CardTitle>
            <CardDescription>
              <span>النموذج النشط: </span>
              <Badge variant="outline">{activeModel}</Badge>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">اكتب موضوعك أو فكرتك</label>
              <Textarea
                placeholder="اكتب هنا الموضوع الذي تريد تطويره إلى نص أدبي..."
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="min-h-[120px]"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">سياق إضافي (اختياري)</label>
              <Textarea
                placeholder="أضف أي معلومات إضافية أو سياق يساعد في إنتاج محتوى أفضل..."
                value={context}
                onChange={(e) => setContext(e.target.value)}
                className="min-h-[80px]"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={handleGenerate}
                disabled={!prompt.trim() || isGenerating}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    جاري الإنتاج...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    إنتاج النص
                  </>
                )}
              </Button>
              <Button variant="outline" disabled={isGenerating}>
                <BookOpen className="w-4 h-4 mr-2" />
                أمثلة للإلهام
              </Button>
            </div>

            {generatedText && (
              <div className="mt-6">
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium">النص المُولد</label>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={copyToClipboard}>
                      <Copy className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={downloadText}>
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg leading-relaxed text-justify whitespace-pre-wrap">
                  {generatedText}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>إعدادات الكتابة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">نوع المحتوى</label>
              <Select value={contentType} onValueChange={setContentType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {contentTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">أسلوب الكتابة</label>
              <Select value={writingStyle} onValueChange={setWritingStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {writingStyles.map((style) => (
                    <SelectItem key={style} value={style}>{style}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                طول النص: {textLength[0]} كلمة
              </label>
              <Slider
                value={textLength}
                onValueChange={setTextLength}
                max={2000}
                min={100}
                step={50}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium">أمثلة للإلهام</label>
              {samplePrompts.map((example, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="w-full text-right justify-start text-xs h-auto p-2"
                  onClick={() => setPrompt(example)}
                >
                  {example.substring(0, 60)}...
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* نصائح الكتابة المتقدمة */}
      <WritingTips selectedAuthor={writingStyle} />
    </div>
  );
};
