// اختبار شامل لجميع النماذج المتاحة
const fs = require('fs');

// قراءة مفاتيح API من ملف البيئة
const envContent = fs.readFileSync('.env.local', 'utf8');
const GROQ_API_KEY = envContent.match(/VITE_GROQ_API_KEY=(.+)/)?.[1];

const models = [
  {
    name: 'GPT-4o Mini (OpenAI)',
    provider: 'openai',
    model: 'gpt-4o-mini',
    apiKey: 'OPENAI_API_KEY', // يحتاج إعداد
    url: 'https://api.openai.com/v1/chat/completions'
  },
  {
    name: 'Llama 3.1 8B (Groq)',
    provider: 'groq',
    model: 'llama-3.1-8b-instant',
    apiKey: GROQ_API_KEY,
    url: 'https://api.groq.com/openai/v1/chat/completions'
  },
  {
    name: 'Llama 3.3 70B (Groq)',
    provider: 'groq',
    model: 'llama-3.3-70b-versatile',
    apiKey: GROQ_API_KEY,
    url: 'https://api.groq.com/openai/v1/chat/completions'
  }
];

async function testModel(modelConfig) {
  console.log(`\n🔍 اختبار ${modelConfig.name}...`);
  
  if (!modelConfig.apiKey) {
    console.log(`❌ مفتاح API غير متوفر لـ ${modelConfig.name}`);
    return { success: false, error: 'مفتاح API غير متوفر' };
  }

  try {
    const response = await fetch(modelConfig.url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${modelConfig.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: modelConfig.model,
        messages: [
          {
            role: 'user',
            content: 'اكتب جملة واحدة بالعربية عن الأدب'
          }
        ],
        max_tokens: 50,
        temperature: 0.7,
      }),
    });

    console.log(`📊 حالة الاستجابة: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ خطأ: ${errorText}`);
      return { 
        success: false, 
        error: `HTTP ${response.status}: ${errorText}`,
        status: response.status 
      };
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;
    
    if (content) {
      console.log(`✅ نجح الاختبار!`);
      console.log(`📝 الرد: ${content}`);
      return { 
        success: true, 
        response: content,
        model: modelConfig.model 
      };
    } else {
      console.log(`❌ لا توجد استجابة صالحة`);
      return { success: false, error: 'لا توجد استجابة صالحة' };
    }
    
  } catch (error) {
    console.log(`❌ خطأ في الاتصال: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testAllModels() {
  console.log('🚀 بدء اختبار جميع النماذج...\n');
  
  const results = [];
  
  for (const model of models) {
    const result = await testModel(model);
    results.push({
      name: model.name,
      provider: model.provider,
      model: model.model,
      ...result
    });
    
    // انتظار قصير بين الاختبارات
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // تلخيص النتائج
  console.log('\n📊 ملخص النتائج:');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ نجح: ${successful.length} نموذج`);
  console.log(`❌ فشل: ${failed.length} نموذج\n`);
  
  if (successful.length > 0) {
    console.log('النماذج التي تعمل:');
    successful.forEach(model => {
      console.log(`  ✅ ${model.name} (${model.model})`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\nالنماذج التي لا تعمل:');
    failed.forEach(model => {
      console.log(`  ❌ ${model.name}: ${model.error}`);
    });
  }
  
  // توصيات
  console.log('\n💡 التوصيات:');
  if (successful.some(m => m.provider === 'groq')) {
    console.log('  🎯 Groq يعمل - يمكن استخدام النماذج المجانية');
  }
  if (failed.some(m => m.provider === 'openai')) {
    console.log('  🔑 OpenAI يحتاج مفتاح API صالح');
  }
  if (successful.length === 0) {
    console.log('  ⚠️  لا توجد نماذج تعمل - تحقق من مفاتيح API');
  } else {
    const recommended = successful[0];
    console.log(`  🌟 النموذج الموصى به: ${recommended.name}`);
  }
}

// تشغيل الاختبار
testAllModels().catch(console.error);
