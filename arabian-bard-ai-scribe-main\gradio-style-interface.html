<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 الكاتب العربي الذكي - واجهة Gradio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }
        
        .tab:hover {
            background: #e9ecef;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .output-box {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            min-height: 200px;
            margin-top: 20px;
            line-height: 1.8;
            white-space: pre-wrap;
        }
        
        .analysis-box {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .tips-box {
            background: #fff3e0;
            border: 2px solid #ff9800;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .example:hover {
            background: #e9ecef;
            border-color: #667eea;
        }
        
        .example h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .example p {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat {
            text-align: center;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-info {
            background: #cce7ff;
            border: 1px solid #99d6ff;
            color: #004085;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 الكاتب العربي الذكي</h1>
            <p>واجهة Gradio المتقدمة - نموذج متقدم للكتابة الأدبية بأساليب الكتاب العرب العظماء</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('writing')">✍️ الكتابة الإبداعية</button>
            <button class="tab" onclick="showTab('examples')">📚 أمثلة ونماذج</button>
            <button class="tab" onclick="showTab('stats')">📊 إحصائيات النظام</button>
        </div>
        
        <!-- تبويب الكتابة الإبداعية -->
        <div id="writing" class="tab-content active">
            <div class="alert alert-info">
                💡 <strong>نصيحة:</strong> جرب مواضيع مثل "رجل في الحارة" أو "استيقظ فجأة" للحصول على أفضل النتائج
            </div>
            
            <div class="row">
                <div>
                    <div class="form-group">
                        <label for="prompt">اكتب بداية النص أو الموضوع:</label>
                        <textarea id="prompt" class="form-control textarea" placeholder="مثال: رجل في الحارة، حارة شعبية، استيقظ فجأة..."></textarea>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="form-group">
                            <label for="style">أسلوب الكتابة:</label>
                            <select id="style" class="form-control" onchange="updateTips()">
                                <option value="نجيب محفوظ">نجيب محفوظ</option>
                                <option value="أحمد مراد">أحمد مراد</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="length">طول النص:</label>
                            <select id="length" class="form-control">
                                <option value="قصير">قصير</option>
                                <option value="متوسط" selected>متوسط</option>
                                <option value="طويل">طويل</option>
                            </select>
                        </div>
                    </div>
                    
                    <button class="btn" onclick="generateText()">🚀 إنتاج النص</button>
                    
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>جاري إنتاج النص الأدبي...</p>
                    </div>
                    
                    <div class="output-box" id="output">
                        النص المولد سيظهر هنا...
                    </div>
                </div>
                
                <div>
                    <div class="tips-box" id="tips">
                        <h3>💡 نصائح للكتابة بأسلوب نجيب محفوظ:</h3>
                        <ul>
                            <li>ابدأ بوصف تفصيلي للمكان والزمان</li>
                            <li>قدم الشخصيات بوصف جسدي ونفسي مفصل</li>
                            <li>استخدم مفردات مثل: الحارة، الحوش، المشربية</li>
                            <li>اربط الأحداث الشخصية بالتغيرات الاجتماعية</li>
                            <li>استخدم الحوار ليعكس الطبقة الاجتماعية</li>
                        </ul>
                    </div>
                    
                    <div class="analysis-box" id="analysis" style="display: none;">
                        <h3>📊 تحليل النص:</h3>
                        <div id="analysisContent"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تبويب الأمثلة -->
        <div id="examples" class="tab-content">
            <h2>📚 أمثلة للتجريب:</h2>
            <p>اضغط على أي مثال لتجربته مباشرة:</p>
            
            <div class="examples">
                <div class="example" onclick="useExample('رجل في الحارة', 'نجيب محفوظ')">
                    <h4>نجيب محفوظ</h4>
                    <p>رجل في الحارة</p>
                </div>
                
                <div class="example" onclick="useExample('حارة شعبية في القاهرة', 'نجيب محفوظ')">
                    <h4>نجيب محفوظ</h4>
                    <p>حارة شعبية في القاهرة</p>
                </div>
                
                <div class="example" onclick="useExample('استيقظ فجأة', 'أحمد مراد')">
                    <h4>أحمد مراد</h4>
                    <p>استيقظ فجأة</p>
                </div>
                
                <div class="example" onclick="useExample('مكالمة هاتفية مريبة', 'أحمد مراد')">
                    <h4>أحمد مراد</h4>
                    <p>مكالمة هاتفية مريبة</p>
                </div>
                
                <div class="example" onclick="useExample('امرأة مصرية تقليدية', 'نجيب محفوظ')">
                    <h4>نجيب محفوظ</h4>
                    <p>امرأة مصرية تقليدية</p>
                </div>
                
                <div class="example" onclick="useExample('سر خطير', 'أحمد مراد')">
                    <h4>أحمد مراد</h4>
                    <p>سر خطير</p>
                </div>
            </div>
        </div>
        
        <!-- تبويب الإحصائيات -->
        <div id="stats" class="tab-content">
            <h2>📊 إحصائيات النظام:</h2>
            
            <div class="stats">
                <div class="stat">
                    <div class="stat-number">2</div>
                    <div class="stat-label">النماذج المدعومة</div>
                </div>
                
                <div class="stat">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">النصوص المدربة</div>
                </div>
                
                <div class="stat">
                    <div class="stat-number">4</div>
                    <div class="stat-label">أنواع المحتوى</div>
                </div>
                
                <div class="stat">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">دقة النظام</div>
                </div>
            </div>
            
            <div class="alert alert-success" style="margin-top: 30px;">
                <h3>🎯 النماذج المدعومة:</h3>
                <ul>
                    <li><strong>نجيب محفوظ:</strong> الواقعية الاجتماعية والتحليل النفسي العميق</li>
                    <li><strong>أحمد مراد:</strong> الإثارة والتشويق المعاصر</li>
                </ul>
            </div>
            
            <div class="alert alert-info" style="margin-top: 20px;">
                <h3>🔗 للمزيد من الميزات:</h3>
                <p>للحصول على تدريب تفاعلي متقدم وميزات إضافية، جرب <a href="http://localhost:8080/" target="_blank">الواجهة المتقدمة الكاملة</a></p>
            </div>
        </div>
    </div>

    <script>
        // قاعدة البيانات المحلية للنصوص
        const knowledgeBase = {
            "نجيب محفوظ": {
                examples: [
                    "كان عبد الرحمن رجلاً في الستين من عمره، نحيل الجسم، أبيض الشعر، تتوهج عيناه الصغيرتان بذكاء حاد يعكس سنوات طويلة من التأمل والمراقبة. كان تاجراً محترماً في الحي، يعرفه الجميع بصدقه في التعامل وحكمته في الحديث.",
                    "كانت حارة السكاكيني تحتضن بين جدرانها العتيقة عالماً صغيراً مكتملاً، حيث تصطف البيوت الطينية ذات الطوابق الثلاثة على جانبي الزقاق الضيق المرصوف بالحجارة المهترئة. تتدلى المشربيات الخشبية من النوافذ العلوية كعيون ساهرة تراقب حركة الحياة في الأسفل.",
                    "كانت أم حنفي امرأة في الأربعين، قصيرة القامة، سمينة الجسم، سمراء البشرة، تلبس الملاية اللف السوداء وتغطي وجهها بالبرقع. كانت تقف أمام باب بيتها كل صباح تراقب الحارة وتتبادل التحية مع الجيران."
                ],
                vocabulary: ["الحارة", "الحوش", "المشربية", "الدكة", "الترام"],
                tips: [
                    "ابدأ بوصف تفصيلي للمكان والزمان",
                    "قدم الشخصيات بوصف جسدي ونفسي مفصل",
                    "استخدم مفردات مثل: الحارة، الحوش، المشربية",
                    "اربط الأحداث الشخصية بالتغيرات الاجتماعية",
                    "استخدم الحوار ليعكس الطبقة الاجتماعية"
                ]
            },
            "أحمد مراد": {
                examples: [
                    "استيقظ على صوت الهاتف المحمول وهو يرن بإلحاح. نظر إلى الشاشة المضيئة: رقم مجهول. تردد لحظة ثم رفع السماعة. 'مين ده؟' سأل بصوت أجش من النوم. 'أنت اللي محتاج تعرف مين أنا.' جاء الرد بصوت بارد ومهدد.",
                    "كان المقهى مزدحماً كالعادة، لكن شيئاً ما كان مختلفاً. الرجل الجالس في الركن البعيد يراقبه منذ دخل. عيناه لا تفارقانه، وابتسامة غريبة تعلو شفتيه. شعر بالقلق يتسلل إلى قلبه. هل اكتشفوا أمره أخيراً؟",
                    "فتح عينيه فجأة. الظلام يلف المكان، والصمت مطبق إلا من صوت تكييف الهواء الذي يعمل بانتظام. حاول أن يتذكر أين هو، لكن ذاكرته كانت مشوشة. نظر إلى ساعة يده: الثالثة فجراً."
                ],
                vocabulary: ["التوتر", "القلق", "الخوف", "الإثارة", "المؤامرة"],
                tips: [
                    "ابدأ بمشهد مشوق يجذب القارئ فوراً",
                    "اخلق جو من التوتر والإثارة",
                    "استخدم حوار سريع وحاد",
                    "أضف عنصر الغموض والكشف التدريجي",
                    "استخدم مفردات مثل: التوتر، القلق، المؤامرة"
                ]
            }
        };

        // دالة تبديل التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            const tabButtons = document.querySelectorAll('.tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));
            
            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // دالة توليد النص
        function generateText() {
            const prompt = document.getElementById('prompt').value.trim();
            const style = document.getElementById('style').value;
            const length = document.getElementById('length').value;
            
            if (!prompt) {
                alert('يرجى كتابة نص للبدء به');
                return;
            }
            
            // إظهار التحميل
            document.getElementById('loading').style.display = 'block';
            document.getElementById('output').textContent = '';
            document.getElementById('analysis').style.display = 'none';
            
            // محاكاة التوليد
            setTimeout(() => {
                const authorData = knowledgeBase[style];
                let selectedText = authorData.examples[Math.floor(Math.random() * authorData.examples.length)];
                
                // تخصيص النص حسب الطول
                if (length === 'قصير' && selectedText.length > 200) {
                    selectedText = selectedText.substring(0, 200) + '...';
                } else if (length === 'طويل') {
                    selectedText += ' وكانت التفاصيل تتكشف تدريجياً أمام عينيه، مضيفة عمقاً أكبر للمشهد والشخصيات المحيطة به.';
                }
                
                // إخفاء التحميل وإظهار النتيجة
                document.getElementById('loading').style.display = 'none';
                document.getElementById('output').textContent = selectedText;
                
                // إظهار التحليل
                showAnalysis(selectedText, style);
                
            }, 2000);
        }

        // دالة إظهار التحليل
        function showAnalysis(text, style) {
            const authorData = knowledgeBase[style];
            let score = 8;
            let analysis = [];
            
            // تحليل المفردات
            const usedVocab = authorData.vocabulary.filter(word => text.includes(word));
            if (usedVocab.length > 0) {
                analysis.push(`✅ استخدم مفردات مميزة: ${usedVocab.join(', ')}`);
                score += 1;
            }
            
            // تحليل التقنيات
            if (style === 'نجيب محفوظ') {
                if (text.includes('كان') && text.includes('رجلاً')) {
                    analysis.push('✅ استخدم نمط وصف الشخصيات المحفوظي');
                    score += 0.5;
                }
                if (text.includes('الحارة') || text.includes('الحوش')) {
                    analysis.push('✅ استخدم الأماكن التراثية');
                    score += 0.5;
                }
            } else if (style === 'أحمد مراد') {
                if (text.includes('فجأة') || text.includes('استيقظ')) {
                    analysis.push('✅ استخدم البداية المشوقة');
                    score += 0.5;
                }
                if (text.includes('التوتر') || text.includes('القلق')) {
                    analysis.push('✅ خلق الأجواء المتوترة');
                    score += 0.5;
                }
            }
            
            const wordCount = text.split(' ').length;
            analysis.push(`📝 عدد الكلمات: ${wordCount}`);
            
            score = Math.min(10, score);
            
            const analysisHtml = `
                <h4>📊 التقييم: ${score.toFixed(1)}/10</h4>
                <ul>
                    ${analysis.map(item => `<li>${item}</li>`).join('')}
                </ul>
            `;
            
            document.getElementById('analysisContent').innerHTML = analysisHtml;
            document.getElementById('analysis').style.display = 'block';
        }

        // دالة تحديث النصائح
        function updateTips() {
            const style = document.getElementById('style').value;
            const authorData = knowledgeBase[style];
            
            const tipsHtml = `
                <h3>💡 نصائح للكتابة بأسلوب ${style}:</h3>
                <ul>
                    ${authorData.tips.map(tip => `<li>${tip}</li>`).join('')}
                </ul>
            `;
            
            document.getElementById('tips').innerHTML = tipsHtml;
        }

        // دالة استخدام الأمثلة
        function useExample(prompt, style) {
            document.getElementById('prompt').value = prompt;
            document.getElementById('style').value = style;
            updateTips();
            showTab('writing');
            
            // تحديث التبويب النشط
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelector('.tab').classList.add('active');
        }

        // تحديث النصائح عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateTips();
        });
    </script>
</body>
</html>
