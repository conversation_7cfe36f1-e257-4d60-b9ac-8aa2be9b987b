import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ContentRequest {
  prompt: string;
  contentType: 'story' | 'poem' | 'article' | 'dialogue' | 'description';
  writingStyle: string;
  activeModel?: string;
  length?: 'short' | 'medium' | 'long';
  context?: string;
}

// تكوين النماذج المختلفة
const modelConfigs = {
  'gpt-4o-mini': {
    provider: 'openai',
    model: 'gpt-4o-mini',
    maxTokens: 1500,
    temperature: 0.8
  },
  'gpt-4o': {
    provider: 'openai',
    model: 'gpt-4o',
    maxTokens: 2000,
    temperature: 0.7
  },
  'llama-3.1-70b': {
    provider: 'groq',
    model: 'llama-3.1-8b-instant',
    maxTokens: 1500,
    temperature: 0.8
  },
  'claude-3-sonnet': {
    provider: 'anthropic',
    model: 'claude-3-sonnet-20240229',
    maxTokens: 1500,
    temperature: 0.8
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { 
      prompt, 
      contentType, 
      writingStyle, 
      activeModel = 'gpt-4o-mini',
      length = 'medium',
      context 
    }: ContentRequest = await req.json()

    // الحصول على تكوين النموذج
    const modelConfig = modelConfigs[activeModel] || modelConfigs['gpt-4o-mini']
    
    // بناء السياق المحسن للنموذج
    const systemPrompt = buildContentPrompt(contentType, writingStyle, length, activeModel)
    
    // إعداد الرسائل
    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: buildUserPrompt(prompt, context, contentType) }
    ]

    // استدعاء النموذج المناسب
    let generatedContent: string
    
    switch (modelConfig.provider) {
      case 'openai':
        generatedContent = await callOpenAI(messages, modelConfig)
        break
      case 'groq':
        generatedContent = await callGroq(messages, modelConfig)
        break
      case 'anthropic':
        generatedContent = await callAnthropic(messages, modelConfig)
        break
      default:
        generatedContent = await callOpenAI(messages, modelConfig)
    }

    return new Response(
      JSON.stringify({ 
        content: generatedContent,
        model: activeModel,
        contentType,
        writingStyle
      }),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )
  }
})

// بناء السياق المحسن حسب نوع المحتوى
function buildContentPrompt(contentType: string, writingStyle: string, length: string, activeModel: string): string {
  const basePrompt = `أنت كاتب محترف متخصص في الأدب العربي باستخدام نموذج ${activeModel}.
أسلوب الكتابة المطلوب: ${writingStyle}
طول المحتوى: ${length === 'short' ? 'قصير (100-200 كلمة)' : length === 'medium' ? 'متوسط (300-500 كلمة)' : 'طويل (600-1000 كلمة)'}

مهمتك هي كتابة محتوى أدبي عالي الجودة باللغة العربية الفصحى.`

  const contentTypePrompts = {
    'story': `
اكتب قصة قصيرة مشوقة تتضمن:
- بداية جذابة تلفت انتباه القارئ
- شخصيات واضحة ومقنعة
- حبكة متماسكة مع صراع واضح
- حوارات طبيعية ومعبرة
- وصف دقيق للمشاهد والأماكن
- نهاية مؤثرة ومرضية`,
    
    'poem': `
اكتب قصيدة جميلة تتضمن:
- اختيار البحر الشعري المناسب
- صور شعرية مبتكرة وجميلة
- إيقاع موسيقي متناغم
- معنى عميق ومؤثر
- استخدام البلاغة والمحسنات البديعية
- وحدة الموضوع والمشاعر`,
    
    'article': `
اكتب مقالاً أدبياً يتضمن:
- مقدمة شائقة تطرح الموضوع
- أفكار منظمة ومترابطة
- أدلة وشواهد من التراث العربي
- أسلوب واضح وقوي
- خاتمة مؤثرة تلخص الأفكار الرئيسية`,
    
    'dialogue': `
اكتب حواراً طبيعياً ومعبراً يتضمن:
- شخصيات واضحة لكل متحدث
- لغة مناسبة لكل شخصية
- تطور في الحوار يخدم الهدف
- تبادل طبيعي للأدوار
- تعبير عن المشاعر والأفكار`,
    
    'description': `
اكتب وصفاً دقيقاً وجميلاً يتضمن:
- تفاصيل حسية واضحة
- استخدام الحواس الخمس
- صور بلاغية مؤثرة
- ترتيب منطقي للعناصر
- لغة شاعرية وجميلة`
  }

  const styleSpecificPrompts = {
    'نجيب محفوظ': `اكتب بأسلوب نجيب محفوظ:

    الخصائص الأسلوبية:
    - الواقعية الاجتماعية: صور الحياة اليومية في مصر بتفاصيلها الدقيقة
    - التحليل النفسي العميق: اغوص في أعماق الشخصيات ودوافعها الداخلية
    - الوصف التفصيلي: صف الأماكن والأشخاص بدقة متناهية (الحارات، البيوت، الوجوه)
    - اللغة البسيطة العميقة: استخدم لغة واضحة لكن محملة بالمعاني
    - الرمزية الاجتماعية: اجعل الأحداث والشخصيات رموزاً لقضايا أكبر
    - التدرج الزمني: اربط الماضي بالحاضر وأظهر تطور الشخصيات

    تقنيات السرد:
    - السرد بضمير الغائب مع التدخل أحياناً
    - الحوار الطبيعي الذي يعكس طبقات المجتمع المختلفة
    - التركيز على التفاصيل الصغيرة التي تكشف الشخصية
    - استخدام الأمثال والحكم الشعبية
    - وصف الحالة النفسية من خلال الأفعال والسلوكيات`,

    'أحمد مراد': `اكتب بأسلوب أحمد مراد:

    الخصائص الأسلوبية:
    - السرد السينمائي: اكتب كأنك تصور فيلماً، بمشاهد متتالية ومؤثرة
    - التشويق والإثارة: ابن التوتر تدريجياً واتركه يتصاعد
    - الحبكة المعقدة: اربط الأحداث بخيوط متشابكة تنكشف تدريجياً
    - الشخصيات المركبة: اجعل كل شخصية لها جوانب متعددة ومتناقضة
    - اللغة العصرية: استخدم لغة الشارع المصري المعاصر مع الفصحى
    - الغموض والكشف: اخف المعلومات ثم اكشفها في اللحظة المناسبة

    تقنيات السرد:
    - التنقل بين الأزمنة والأماكن بسلاسة
    - استخدام الفلاش باك والفلاش فورورد
    - الحوار السريع والحاد الذي يدفع الأحداث
    - وصف الأجواء المشحونة والمواقف المتوترة
    - استخدام التفاصيل التقنية والمعاصرة (التكنولوجيا، وسائل التواصل)`,

    'غسان كنفاني': 'ركز على القضايا الاجتماعية والسياسية، الرمزية، والأسلوب الشاعري',
    'أحلام مستغانمي': 'استخدم الرومانسية، اللغة الشاعرية، والعمق العاطفي',
    'طه حسين': 'اعتمد على الأسلوب الأكاديمي، التحليل العميق، والثقافة الواسعة'
  }

  return `${basePrompt}

${contentTypePrompts[contentType] || ''}

أسلوب ${writingStyle}: ${styleSpecificPrompts[writingStyle] || ''}

إرشادات عامة:
- استخدم اللغة العربية الفصحى الجميلة
- اجعل المحتوى أصيلاً ومبتكراً
- احترم الثقافة العربية والإسلامية
- اهتم بجمال اللغة وقوة التعبير
- تأكد من سلامة النحو والإملاء`
}

// بناء رسالة المستخدم
function buildUserPrompt(prompt: string, context: string | undefined, contentType: string): string {
  let userPrompt = `اكتب ${contentType === 'story' ? 'قصة' : contentType === 'poem' ? 'قصيدة' : contentType === 'article' ? 'مقالاً' : contentType === 'dialogue' ? 'حواراً' : 'وصفاً'} عن: ${prompt}`
  
  if (context) {
    userPrompt += `\n\nالسياق الإضافي: ${context}`
  }
  
  return userPrompt
}

// استدعاء OpenAI API
async function callOpenAI(messages: any[], modelConfig: any): Promise<string> {
  const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
  
  if (!openaiApiKey) {
    throw new Error('OpenAI API key not found')
  }

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openaiApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: modelConfig.model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    }),
  })

  if (!response.ok) {
    const errorData = await response.text()
    throw new Error(`OpenAI API error: ${response.status} - ${errorData}`)
  }

  const data = await response.json()
  return data.choices[0].message.content
}

// استدعاء Groq API
async function callGroq(messages: any[], modelConfig: any): Promise<string> {
  const groqApiKey = Deno.env.get('GROQ_API_KEY')
  
  if (!groqApiKey) {
    throw new Error('Groq API key not found')
  }

  const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${groqApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: modelConfig.model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    }),
  })

  if (!response.ok) {
    const errorData = await response.text()
    throw new Error(`Groq API error: ${response.status} - ${errorData}`)
  }

  const data = await response.json()
  return data.choices[0].message.content
}

// استدعاء Anthropic API
async function callAnthropic(messages: any[], modelConfig: any): Promise<string> {
  const anthropicApiKey = Deno.env.get('ANTHROPIC_API_KEY')
  
  if (!anthropicApiKey) {
    throw new Error('Anthropic API key not found')
  }

  const systemMessage = messages.find(m => m.role === 'system')?.content || ''
  const conversationMessages = messages.filter(m => m.role !== 'system')

  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'x-api-key': anthropicApiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify({
      model: modelConfig.model,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
      system: systemMessage,
      messages: conversationMessages,
    }),
  })

  if (!response.ok) {
    const errorData = await response.text()
    throw new Error(`Anthropic API error: ${response.status} - ${errorData}`)
  }

  const data = await response.json()
  return data.content[0].text
}
