import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { 
  Download, 
  FileText, 
  Brain, 
  Zap, 
  BarChart3, 
  Code, 
  Database,
  Rocket,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { 
  generateAdvancedTrainingFile, 
  generateTrainingReport,
  type JSONLDataset 
} from "@/utils/datasetGenerator";

export const AdvancedTraining = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedData, setGeneratedData] = useState<{
    jsonl: string;
    json: string;
    metadata: any;
    colabCode: string;
  } | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const { toast } = useToast();

  // توليد بيانات التدريب
  const generateTrainingData = async () => {
    setIsGenerating(true);
    try {
      // محاكاة وقت المعالجة
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const data = generateAdvancedTrainingFile();
      setGeneratedData(data);
      
      toast({
        title: "تم توليد البيانات بنجاح! 🎉",
        description: `تم إنشاء ${data.metadata.totalSamples} نص تدريبي`,
      });
    } catch (error) {
      toast({
        title: "خطأ في توليد البيانات",
        description: "حدث خطأ أثناء توليد بيانات التدريب",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // تحميل الملفات
  const downloadFile = (content: string, filename: string, type: string = "text/plain") => {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">التدريب المتقدم مع AraGPT2</h2>
        <p className="text-slate-600">
          تدريب نماذج مخصصة على الأساليب الأدبية العربية باستخدام Google Colab
        </p>
      </div>

      {/* نظرة عامة */}
      <div className="grid md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Database className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-slate-600">بيانات التدريب</p>
                <p className="text-2xl font-bold">
                  {generatedData ? generatedData.metadata.totalSamples : '---'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Brain className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-slate-600">الكتاب المدعومون</p>
                <p className="text-2xl font-bold">
                  {generatedData ? generatedData.metadata.authors.length : '---'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm text-slate-600">فئات المحتوى</p>
                <p className="text-2xl font-bold">
                  {generatedData ? generatedData.metadata.categories.length : '---'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="generate">توليد البيانات</TabsTrigger>
          <TabsTrigger value="colab">كود Colab</TabsTrigger>
          <TabsTrigger value="files">الملفات</TabsTrigger>
          <TabsTrigger value="guide">دليل التدريب</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Rocket className="w-5 h-5 text-blue-600" />
                نظام التدريب المتقدم
              </CardTitle>
              <CardDescription>
                تدريب نماذج AraGPT2 مخصصة للكتابة الأدبية العربية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-700">✅ المزايا:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• تدريب على أساليب كتاب محددين</li>
                    <li>• بيانات عالية الجودة ومصنفة</li>
                    <li>• دعم Google Colab المجاني</li>
                    <li>• نماذج قابلة للتخصيص</li>
                    <li>• تقييم مستمر للأداء</li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-700">🎯 الاستخدامات:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• توليد نصوص بأساليب محددة</li>
                    <li>• مساعدة الكتاب في التطوير</li>
                    <li>• تحليل الأساليب الأدبية</li>
                    <li>• التعليم والتدريب</li>
                    <li>• البحث الأكاديمي</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-900">متطلبات التدريب:</h4>
                    <ul className="text-sm text-amber-800 mt-1 space-y-1">
                      <li>• حساب Google Colab (يفضل Pro للسرعة)</li>
                      <li>• 2-4 ساعات وقت تدريب</li>
                      <li>• ذاكرة GPU كافية (12GB+)</li>
                      <li>• اتصال إنترنت مستقر</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="generate" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5 text-green-600" />
                توليد بيانات التدريب
              </CardTitle>
              <CardDescription>
                إنشاء ملفات التدريب من قاعدة المعرفة الأدبية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!generatedData ? (
                <div className="text-center py-8">
                  <Button 
                    onClick={generateTrainingData}
                    disabled={isGenerating}
                    size="lg"
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        جاري التوليد...
                      </>
                    ) : (
                      <>
                        <Database className="w-4 h-4 mr-2" />
                        توليد بيانات التدريب
                      </>
                    )}
                  </Button>
                  <p className="text-sm text-slate-600 mt-2">
                    سيتم إنشاء ملفات التدريب من قاعدة المعرفة الأدبية
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      <h4 className="font-medium text-green-900">تم توليد البيانات بنجاح!</h4>
                    </div>
                    <div className="grid md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">إجمالي النصوص:</span>
                        <span className="ml-2">{generatedData.metadata.totalSamples}</span>
                      </div>
                      <div>
                        <span className="font-medium">الكتاب:</span>
                        <span className="ml-2">{generatedData.metadata.authors.join(', ')}</span>
                      </div>
                      <div>
                        <span className="font-medium">الفئات:</span>
                        <span className="ml-2">{generatedData.metadata.categories.length}</span>
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <Button 
                      onClick={() => downloadFile(generatedData.jsonl, 'arabic_literary_dataset.jsonl')}
                      variant="outline"
                      className="w-full"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      تحميل ملف JSONL
                    </Button>
                    <Button 
                      onClick={() => downloadFile(generatedData.json, 'training_data.json')}
                      variant="outline"
                      className="w-full"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      تحميل ملف JSON
                    </Button>
                  </div>

                  <Button 
                    onClick={generateTrainingData}
                    variant="outline"
                    className="w-full"
                  >
                    <Database className="w-4 h-4 mr-2" />
                    إعادة توليد البيانات
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="colab" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="w-5 h-5 text-purple-600" />
                كود Google Colab
              </CardTitle>
              <CardDescription>
                كود جاهز للتدريب في Google Colab
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {generatedData ? (
                <>
                  <Textarea
                    value={generatedData.colabCode}
                    readOnly
                    className="min-h-[400px] font-mono text-sm"
                  />
                  <div className="flex gap-2">
                    <Button 
                      onClick={() => downloadFile(generatedData.colabCode, 'arabic_training_colab.py')}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      تحميل كود Python
                    </Button>
                    <Button 
                      onClick={() => navigator.clipboard.writeText(generatedData.colabCode)}
                      variant="outline"
                    >
                      نسخ الكود
                    </Button>
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <p className="text-slate-600">قم بتوليد البيانات أولاً لرؤية كود Colab</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="files" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-orange-600" />
                الملفات المولدة
              </CardTitle>
              <CardDescription>
                جميع الملفات اللازمة للتدريب
              </CardDescription>
            </CardHeader>
            <CardContent>
              {generatedData ? (
                <div className="space-y-4">
                  <div className="grid gap-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">arabic_literary_dataset.jsonl</h4>
                          <p className="text-sm text-slate-600">ملف بيانات التدريب الرئيسي</p>
                        </div>
                        <Badge variant="outline">JSONL</Badge>
                      </div>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">training_data.json</h4>
                          <p className="text-sm text-slate-600">بيانات التدريب بصيغة JSON</p>
                        </div>
                        <Badge variant="outline">JSON</Badge>
                      </div>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">arabic_training_colab.py</h4>
                          <p className="text-sm text-slate-600">كود التدريب لـ Google Colab</p>
                        </div>
                        <Badge variant="outline">Python</Badge>
                      </div>
                    </div>
                  </div>

                  <Button 
                    onClick={() => {
                      downloadFile(generatedData.jsonl, 'arabic_literary_dataset.jsonl');
                      downloadFile(generatedData.json, 'training_data.json');
                      downloadFile(generatedData.colabCode, 'arabic_training_colab.py');
                    }}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    تحميل جميع الملفات
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-slate-600">لا توجد ملفات مولدة بعد</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="guide" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-red-600" />
                دليل التدريب خطوة بخطوة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">1</div>
                  <div>
                    <h4 className="font-medium">توليد البيانات</h4>
                    <p className="text-sm text-slate-600">انتقل لتبويب "توليد البيانات" واضغط على زر التوليد</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">2</div>
                  <div>
                    <h4 className="font-medium">تحميل الملفات</h4>
                    <p className="text-sm text-slate-600">حمل ملف JSONL وكود Python من تبويب "الملفات"</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">3</div>
                  <div>
                    <h4 className="font-medium">فتح Google Colab</h4>
                    <p className="text-sm text-slate-600">اذهب إلى colab.research.google.com وأنشئ notebook جديد</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">4</div>
                  <div>
                    <h4 className="font-medium">رفع الملفات</h4>
                    <p className="text-sm text-slate-600">ارفع ملف JSONL إلى Colab والصق كود Python</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">5</div>
                  <div>
                    <h4 className="font-medium">تشغيل التدريب</h4>
                    <p className="text-sm text-slate-600">شغل الكود واتركه يعمل لعدة ساعات</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold">6</div>
                  <div>
                    <h4 className="font-medium">تحميل النموذج</h4>
                    <p className="text-sm text-slate-600">حمل النموذج المدرب واستخدمه في مشروعك</p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">💡 نصائح مهمة:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• استخدم Google Colab Pro للحصول على GPU أقوى</li>
                  <li>• احفظ النموذج بانتظام أثناء التدريب</li>
                  <li>• راقب loss للتأكد من التحسن</li>
                  <li>• اختبر النموذج بعد كل epoch</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
