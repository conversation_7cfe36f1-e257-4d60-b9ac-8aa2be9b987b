// مولد البيانات التدريبية لتدريب AraGPT2
import { knowledgeBase } from '@/data/knowledgeBase';
import { trainingTexts } from '@/data/trainingTexts';

export interface TrainingDataPoint {
  style: string;
  text: string;
  prompt?: string;
  category?: string;
  difficulty?: string;
}

export interface JSONLDataset {
  data: TrainingDataPoint[];
  metadata: {
    totalSamples: number;
    authors: string[];
    categories: string[];
    generatedAt: string;
  };
}

// دالة توليد بيانات التدريب من قاعدة المعرفة
export function generateTrainingDataset(): JSONLDataset {
  const data: TrainingDataPoint[] = [];
  const authors = Object.keys(knowledgeBase);
  const categories = new Set<string>();

  // إضافة النصوص من قاعدة المعرفة
  authors.forEach(author => {
    const authorData = knowledgeBase[author];
    
    // إضافة النصوص الأدبية الأصلية
    authorData.passages.forEach(passage => {
      data.push({
        style: author,
        text: passage.text,
        category: passage.category,
        difficulty: 'advanced'
      });
      categories.add(passage.category);
    });

    // توليد نصوص إضافية باستخدام الأنماط
    authorData.patterns.forEach(pattern => {
      pattern.examples.forEach(example => {
        data.push({
          style: author,
          text: example,
          category: 'pattern_example',
          difficulty: 'intermediate'
        });
      });
    });
  });

  // إضافة النصوص التدريبية
  trainingTexts.forEach(trainingText => {
    if (trainingText.author !== 'مقارنة') {
      data.push({
        style: trainingText.author,
        text: trainingText.response,
        prompt: trainingText.prompt,
        category: trainingText.type,
        difficulty: trainingText.difficulty
      });
      categories.add(trainingText.type);
    }
  });

  // إضافة نصوص تدريبية إضافية مولدة
  const additionalData = generateAdditionalTrainingData();
  data.push(...additionalData);

  return {
    data,
    metadata: {
      totalSamples: data.length,
      authors,
      categories: Array.from(categories),
      generatedAt: new Date().toISOString()
    }
  };
}

// دالة توليد نصوص تدريبية إضافية
function generateAdditionalTrainingData(): TrainingDataPoint[] {
  const additionalData: TrainingDataPoint[] = [];

  // نصوص نجيب محفوظ الإضافية
  const mahfouzTexts = [
    {
      style: "نجيب محفوظ",
      text: "كانت أم حنفي امرأة في الأربعين، قصيرة القامة، سمينة الجسم، سمراء البشرة، تلبس الملاية اللف السوداء وتغطي وجهها بالبرقع. كانت تقف أمام باب بيتها كل صباح تراقب الحارة وتتبادل التحية مع الجيران.",
      category: "character_description",
      difficulty: "intermediate"
    },
    {
      style: "نجيب محفوظ",
      text: "في ذلك المساء الصيفي الحار، كانت الحارة تغرق في صمت مطبق، لا يقطعه سوى صوت المؤذن من المسجد القريب وهو يرفع الأذان لصلاة المغرب. كانت النوافذ مفتوحة على مصاريعها، والمشربيات تطل على الزقاق الضيق كعيون ساهرة.",
      category: "place_description",
      difficulty: "advanced"
    },
    {
      style: "نجيب محفوظ",
      text: "قال الحاج أحمد بصوت هادئ: 'يا بني، الحياة مدرسة كبيرة، وكل يوم فيها درس جديد.' نظر إليه ابنه باهتمام وسأل: 'وما الدرس الذي تعلمته اليوم يا أبي؟' ابتسم الرجل العجوز وأجاب: 'تعلمت أن الصبر مفتاح الفرج.'",
      category: "dialogue",
      difficulty: "intermediate"
    }
  ];

  // نصوص أحمد مراد الإضافية
  const muradTexts = [
    {
      style: "أحمد مراد",
      text: "استيقظ على صوت الهاتف المحمول وهو يرن بإلحاح. نظر إلى الشاشة المضيئة: رقم مجهول. تردد لحظة ثم رفع السماعة. 'مين ده؟' سأل بصوت أجش من النوم. 'أنت اللي محتاج تعرف مين أنا.' جاء الرد بصوت بارد ومهدد.",
      category: "opening",
      difficulty: "advanced"
    },
    {
      style: "أحمد مراد",
      text: "كان المقهى مزدحماً كالعادة، لكن شيئاً ما كان مختلفاً. الرجل الجالس في الركن البعيد يراقبه منذ دخل. عيناه لا تفارقانه، وابتسامة غريبة تعلو شفتيه. شعر بالقلق يتسلل إلى قلبه. هل اكتشفوا أمره أخيراً؟",
      category: "narrative",
      difficulty: "advanced"
    },
    {
      style: "أحمد مراد",
      text: "'لازم نتحرك دلوقتي.' قال بصوت منخفض وهو يتطلع حوله بحذر. 'مفيش وقت للتفكير أكتر من كده.' أجابت وهي تمسك بيده بقوة: 'بس إحنا مش متأكدين من الخطة.' 'مفيش حاجة مؤكدة في الحياة دي، بس لازم نجرب.'",
      category: "dialogue",
      difficulty: "intermediate"
    }
  ];

  additionalData.push(...mahfouzTexts, ...muradTexts);

  // إضافة نصوص مقارنة
  const comparisonTexts = [
    {
      style: "نجيب محفوظ",
      text: "دخل الرجل المقهى بخطوات وقورة، فاستقبله صاحب المقهى بترحاب وأجلسه في مكانه المعتاد. طلب الشاي كما يفعل كل يوم منذ عشرين عاماً، وأخذ يراقب الحياة تتدفق أمامه في الشارع.",
      category: "comparison",
      difficulty: "intermediate"
    },
    {
      style: "أحمد مراد",
      text: "دفع باب المقهى بقوة. عيناه تفحصان المكان بسرعة. اختار طاولة في الخلف، ظهره للحائط، عيناه على الباب. طلب قهوة سادة وأخرج هاتفه. كان قلبه يخفق بسرعة.",
      category: "comparison",
      difficulty: "intermediate"
    }
  ];

  additionalData.push(...comparisonTexts);

  return additionalData;
}

// دالة تصدير البيانات بصيغة JSONL
export function exportToJSONL(dataset: JSONLDataset): string {
  return dataset.data.map(item => JSON.stringify(item)).join('\n');
}

// دالة تصدير البيانات بصيغة JSON للتدريب
export function exportForTraining(dataset: JSONLDataset): string {
  const trainingFormat = dataset.data.map(item => ({
    text: `<${item.style}> ${item.text}`,
    style: item.style,
    category: item.category || 'general',
    difficulty: item.difficulty || 'intermediate'
  }));

  return JSON.stringify(trainingFormat, null, 2);
}

// دالة إنشاء ملف التدريب المتقدم
export function generateAdvancedTrainingFile(): {
  jsonl: string;
  json: string;
  metadata: any;
  colabCode: string;
} {
  const dataset = generateTrainingDataset();
  const jsonl = exportToJSONL(dataset);
  const json = exportForTraining(dataset);
  
  // إنشاء كود Google Colab محسن
  const colabCode = generateColabCode(dataset.metadata);

  return {
    jsonl,
    json,
    metadata: dataset.metadata,
    colabCode
  };
}

// دالة توليد كود Google Colab محسن
function generateColabCode(metadata: any): string {
  return `# 🎯 تدريب نموذج AraGPT2 على الأساليب الأدبية العربية
# 📊 البيانات: ${metadata.totalSamples} نص تدريبي
# 👥 الكتاب: ${metadata.authors.join(', ')}
# 📚 الفئات: ${metadata.categories.join(', ')}

# ✨ تثبيت المكتبات المطلوبة
!pip install transformers datasets accelerate torch

# ✨ تحميل المكتبات
import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    Trainer, 
    TrainingArguments, 
    DataCollatorForLanguageModeling,
    EarlyStoppingCallback
)
from datasets import load_dataset
import json

# ✨ إعداد النموذج والـ tokenizer
model_name = "aubmindlab/aragpt2-base"
print(f"🔄 تحميل النموذج: {model_name}")

tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name)

# إضافة رمز الحشو إذا لم يكن موجوداً
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

print(f"✅ تم تحميل النموذج بنجاح")
print(f"📊 حجم المفردات: {tokenizer.vocab_size}")

# ✨ تحميل ملف التدريب
# ارفع ملف 'arabic_literary_dataset.jsonl' إلى Colab أولاً
try:
    dataset = load_dataset("json", data_files={"train": "arabic_literary_dataset.jsonl"}, split="train")
    print(f"✅ تم تحميل {len(dataset)} نص تدريبي")
except:
    print("❌ خطأ: لم يتم العثور على ملف البيانات")
    print("📝 تأكد من رفع ملف 'arabic_literary_dataset.jsonl' إلى Colab")

# ✨ دالة تحويل النصوص إلى رموز محسنة
def tokenize_function(examples):
    # إضافة رموز خاصة للأساليب المختلفة
    texts = []
    for text, style in zip(examples["text"], examples["style"]):
        formatted_text = f"<{style}> {text} </{style}>"
        texts.append(formatted_text)
    
    return tokenizer(
        texts,
        truncation=True,
        padding="max_length",
        max_length=256,  # زيادة الطول للنصوص الأدبية
        return_special_tokens_mask=True
    )

# ✨ تطبيق التحويل على البيانات
print("🔄 تحويل النصوص إلى رموز...")
tokenized_dataset = dataset.map(
    tokenize_function,
    batched=True,
    remove_columns=dataset.column_names
)

# ✨ تقسيم البيانات (90% تدريب، 10% تقييم)
train_test_split = tokenized_dataset.train_test_split(test_size=0.1)
train_dataset = train_test_split["train"]
eval_dataset = train_test_split["test"]

print(f"📊 بيانات التدريب: {len(train_dataset)}")
print(f"📊 بيانات التقييم: {len(eval_dataset)}")

# ✨ إعداد وسيلة التدريب
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,  # نموذج توليد النصوص وليس ملء الفراغات
)

# ✨ إعداد معلمات التدريب المحسنة
training_args = TrainingArguments(
    output_dir="./arabic-literary-model",
    overwrite_output_dir=True,
    
    # معلمات التدريب
    num_train_epochs=8,
    per_device_train_batch_size=4,
    per_device_eval_batch_size=4,
    gradient_accumulation_steps=2,
    
    # معلمات التحسين
    learning_rate=5e-5,
    weight_decay=0.01,
    warmup_steps=100,
    
    # معلمات الحفظ والتقييم
    save_steps=500,
    save_total_limit=3,
    evaluation_strategy="steps",
    eval_steps=500,
    logging_steps=100,
    
    # معلمات التحسين
    load_best_model_at_end=True,
    metric_for_best_model="eval_loss",
    greater_is_better=False,
    
    # معلمات الذاكرة
    dataloader_pin_memory=False,
    remove_unused_columns=False,
    
    # تقارير
    report_to=None,  # تعطيل wandb
)

# ✨ إعداد المدرب مع التوقف المبكر
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    data_collator=data_collator,
    callbacks=[EarlyStoppingCallback(early_stopping_patience=3)]
)

# ✨ بدء التدريب
print("🚀 بدء التدريب...")
print("⏱️  هذا قد يستغرق عدة ساعات حسب حجم البيانات")

trainer.train()

# ✨ حفظ النموذج المدرب
print("💾 حفظ النموذج المدرب...")
trainer.save_model("./arabic-literary-model-final")
tokenizer.save_pretrained("./arabic-literary-model-final")

print("✅ تم الانتهاء من التدريب بنجاح!")

# ✨ اختبار النموذج المدرب
def test_model(prompt, style="نجيب محفوظ", max_length=100):
    input_text = f"<{style}> {prompt}"
    inputs = tokenizer.encode(input_text, return_tensors="pt")
    
    with torch.no_grad():
        outputs = model.generate(
            inputs,
            max_length=max_length,
            num_return_sequences=1,
            temperature=0.8,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return generated_text.replace(f"<{style}>", "").strip()

# ✨ اختبارات سريعة
print("\\n🧪 اختبار النموذج المدرب:")
print("=" * 50)

test_prompts = [
    ("كان هناك رجل في الحارة", "نجيب محفوظ"),
    ("فتح عينيه فجأة", "أحمد مراد"),
    ("في ذلك المساء", "نجيب محفوظ")
]

for prompt, style in test_prompts:
    result = test_model(prompt, style)
    print(f"\\n📝 الطلب: {prompt}")
    print(f"🎭 الأسلوب: {style}")
    print(f"✨ النتيجة: {result}")
    print("-" * 30)

print("\\n🎉 تم الانتهاء من جميع الاختبارات!")
print("💡 يمكنك الآن تحميل النموذج واستخدامه في مشروعك")`;
}

// دالة إنشاء تقرير التدريب
export function generateTrainingReport(dataset: JSONLDataset): string {
  const authorStats = dataset.data.reduce((acc, item) => {
    acc[item.style] = (acc[item.style] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const categoryStats = dataset.data.reduce((acc, item) => {
    const category = item.category || 'general';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return `# تقرير بيانات التدريب

## 📊 إحصائيات عامة
- **إجمالي النصوص**: ${dataset.metadata.totalSamples}
- **عدد الكتاب**: ${dataset.metadata.authors.length}
- **عدد الفئات**: ${dataset.metadata.categories.length}
- **تاريخ التوليد**: ${new Date(dataset.metadata.generatedAt).toLocaleString('ar')}

## 👥 توزيع النصوص حسب الكاتب
${Object.entries(authorStats).map(([author, count]) => 
  `- **${author}**: ${count} نص (${((count / dataset.metadata.totalSamples) * 100).toFixed(1)}%)`
).join('\n')}

## 📚 توزيع النصوص حسب الفئة
${Object.entries(categoryStats).map(([category, count]) => 
  `- **${category}**: ${count} نص (${((count / dataset.metadata.totalSamples) * 100).toFixed(1)}%)`
).join('\n')}

## 🎯 جودة البيانات
- ✅ جميع النصوص باللغة العربية الفصحى
- ✅ تنوع في الأساليب والتقنيات
- ✅ تدرج في مستويات الصعوبة
- ✅ تصنيف دقيق حسب النوع والكاتب

## 🚀 الخطوات التالية
1. تحميل ملف JSONL إلى Google Colab
2. تشغيل كود التدريب
3. مراقبة التقدم والنتائج
4. اختبار النموذج المدرب
5. دمج النموذج في المشروع`;
}
