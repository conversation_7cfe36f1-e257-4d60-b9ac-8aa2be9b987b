import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Cpu, Zap, Clock, DollarSign, Star, AlertCircle } from "lucide-react";
import { modelConfigs, getModelConfig } from "@/config/models";

interface EnhancedModelSelectionProps {
  activeModel: string;
  setActiveModel: (model: string) => void;
}

export const EnhancedModelSelection = ({ activeModel, setActiveModel }: EnhancedModelSelectionProps) => {
  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'openai': return '🤖';
      case 'groq': return '⚡';
      case 'anthropic': return '🧠';
      default: return '🔧';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'stable': return 'bg-green-100 text-green-800';
      case 'beta': return 'bg-yellow-100 text-yellow-800';
      case 'deprecated': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCostIcon = (cost: string) => {
    switch (cost) {
      case 'low': return <DollarSign className="w-4 h-4 text-green-500" />;
      case 'medium': return <DollarSign className="w-4 h-4 text-yellow-500" />;
      case 'high': return <DollarSign className="w-4 h-4 text-red-500" />;
      default: return <DollarSign className="w-4 h-4 text-gray-500" />;
    }
  };

  const getSpeedIcon = (speed: string) => {
    switch (speed) {
      case 'fast': return <Zap className="w-4 h-4 text-green-500" />;
      case 'medium': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'slow': return <Clock className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">اختيار النموذج المتقدم</h2>
        <p className="text-slate-600">
          اختر النموذج الأنسب لمشروعك بناءً على الجودة والسرعة والتكلفة
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
        {modelConfigs.map((model) => (
          <Card 
            key={model.id} 
            className={`relative transition-all duration-300 ${
              activeModel === model.id 
                ? 'ring-2 ring-emerald-500 shadow-lg' 
                : 'hover:shadow-md'
            }`}
          >
            {model.id === 'gpt-4o-mini' && (
              <Badge className="absolute -top-2 right-4 bg-emerald-500 hover:bg-emerald-600">
                <Star className="w-3 h-3 mr-1" />
                موصى به
              </Badge>
            )}
            
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <span className="text-xl">{getProviderIcon(model.provider)}</span>
                {model.name}
              </CardTitle>
              <CardDescription className="text-sm">{model.description}</CardDescription>
              
              <div className="flex gap-2 mt-2">
                <Badge className={getStatusColor(model.status)}>
                  {model.status === 'stable' ? 'مستقر' : 
                   model.status === 'beta' ? 'تجريبي' : 'مهجور'}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* مؤشرات الأداء */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-1">
                  {getCostIcon(model.cost)}
                  <span className="text-slate-600">
                    {model.cost === 'low' ? 'رخيص' : 
                     model.cost === 'medium' ? 'متوسط' : 'مكلف'}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  {getSpeedIcon(model.speed)}
                  <span className="text-slate-600">
                    {model.speed === 'fast' ? 'سريع' : 
                     model.speed === 'medium' ? 'متوسط' : 'بطيء'}
                  </span>
                </div>
              </div>

              {/* نقاط القوة */}
              <div className="space-y-1">
                <div className="text-sm font-medium text-slate-700">نقاط القوة:</div>
                {model.strengths.slice(0, 2).map((strength, index) => (
                  <div key={index} className="flex items-center gap-1 text-xs">
                    <CheckCircle className="w-3 h-3 text-emerald-500" />
                    <span className="text-slate-600">{strength}</span>
                  </div>
                ))}
              </div>

              {/* الأفضل لـ */}
              <div className="space-y-1">
                <div className="text-sm font-medium text-slate-700">الأفضل لـ:</div>
                <div className="text-xs text-slate-600">
                  {model.bestFor.slice(0, 2).join('، ')}
                </div>
              </div>

              {/* تحذيرات خاصة */}
              {model.id === 'llama-3.1-8b' && (
                <div className="flex items-center gap-1 p-2 bg-yellow-50 rounded text-xs">
                  <AlertCircle className="w-3 h-3 text-yellow-600" />
                  <span className="text-yellow-700">قد يحتاج مفتاح Groq صالح</span>
                </div>
              )}

              <Button 
                className={`w-full text-sm ${
                  activeModel === model.id 
                    ? 'bg-emerald-600 hover:bg-emerald-700' 
                    : 'bg-slate-600 hover:bg-slate-700'
                }`}
                onClick={() => setActiveModel(model.id)}
              >
                {activeModel === model.id ? 'مُحدد حالياً' : 'اختيار'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* معلومات النموذج المحدد */}
      {activeModel && (
        <Card className="bg-emerald-50 border-emerald-200">
          <CardHeader>
            <CardTitle className="text-emerald-800">
              النموذج المحدد: {getModelConfig(activeModel)?.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-semibold text-emerald-800 mb-2">نقاط القوة:</h4>
                <ul className="space-y-1">
                  {getModelConfig(activeModel)?.strengths.map((strength, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="w-3 h-3 text-emerald-600" />
                      <span className="text-emerald-700">{strength}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-emerald-800 mb-2">الأفضل لـ:</h4>
                <ul className="space-y-1">
                  {getModelConfig(activeModel)?.bestFor.map((use, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <Star className="w-3 h-3 text-emerald-600" />
                      <span className="text-emerald-700">{use}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
