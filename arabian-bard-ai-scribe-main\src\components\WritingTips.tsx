import React from 'react';
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BookOpen, Lightbulb, Target, Zap } from "lucide-react";

interface WritingTipsProps {
  selectedAuthor: string;
}

const authorTips = {
  'نجيب محفوظ': {
    icon: BookOpen,
    color: 'bg-amber-100 text-amber-800',
    tips: [
      {
        category: 'الوصف',
        icon: Target,
        advice: 'اهتم بالتفاصيل الصغيرة: لون العيون، شكل الأنف، طريقة المشي - هذه التفاصيل تجعل الشخصية حية'
      },
      {
        category: 'الحوار',
        icon: Zap,
        advice: 'استخدم لهجة كل طبقة اجتماعية: الفقير يتكلم بطريقة، والغني بطريقة أخرى، هذا يضفي واقعية'
      },
      {
        category: 'الرمزية',
        icon: Lightbulb,
        advice: 'اجعل الحارة رمزاً لمصر، والبيت رمزاً للأسرة، والشخصية رمزاً لجيل كامل'
      },
      {
        category: 'التطور',
        icon: BookOpen,
        advice: 'أظهر كيف تتغير الشخصيات عبر الزمن - من الطفولة للشباب للشيخوخة'
      }
    ],
    example: 'مثال: بدلاً من "كان غاضباً"، اكتب "انتفخت أوداجه وتصلبت قبضتاه وهو يحدق في الأرض"'
  },
  
  'أحمد مراد': {
    icon: Zap,
    color: 'bg-red-100 text-red-800',
    tips: [
      {
        category: 'التشويق',
        icon: Target,
        advice: 'ابدأ كل فصل بسؤال أو غموض، واتركه معلقاً حتى النهاية'
      },
      {
        category: 'السرد السينمائي',
        icon: Zap,
        advice: 'اكتب كأنك تصور فيلماً: زاوية الكاميرا، الإضاءة، الصوت - كل شيء مهم'
      },
      {
        category: 'الشخصيات',
        icon: Lightbulb,
        advice: 'اجعل كل شخصية لها سر خفي يُكشف تدريجياً، لا أحد بريء تماماً'
      },
      {
        category: 'الحبكة',
        icon: BookOpen,
        advice: 'اربط الأحداث بخيوط خفية، كل شيء مترابط ولا شيء عشوائي'
      }
    ],
    example: 'مثال: بدلاً من "دخل الغرفة"، اكتب "دفع الباب ببطء، صريره يقطع صمت الليل، عيناه تتفحصان الظلال"'
  }
};

export const WritingTips: React.FC<WritingTipsProps> = ({ selectedAuthor }) => {
  const tips = authorTips[selectedAuthor];
  
  if (!tips) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5" />
            نصائح الكتابة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-slate-600">اختر كاتباً لعرض نصائح الكتابة المخصصة</p>
        </CardContent>
      </Card>
    );
  }

  const IconComponent = tips.icon;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconComponent className="w-5 h-5" />
          نصائح الكتابة بأسلوب {selectedAuthor}
        </CardTitle>
        <Badge className={tips.color}>{selectedAuthor}</Badge>
      </CardHeader>
      <CardContent className="space-y-4">
        {tips.tips.map((tip, index) => {
          const TipIcon = tip.icon;
          return (
            <div key={index} className="border-r-4 border-emerald-500 pr-4 py-2">
              <div className="flex items-center gap-2 mb-2">
                <TipIcon className="w-4 h-4 text-emerald-600" />
                <span className="font-semibold text-slate-800">{tip.category}</span>
              </div>
              <p className="text-slate-600 text-sm leading-relaxed">{tip.advice}</p>
            </div>
          );
        })}
        
        <div className="mt-6 p-4 bg-slate-50 rounded-lg">
          <h4 className="font-semibold text-slate-800 mb-2 flex items-center gap-2">
            <BookOpen className="w-4 h-4" />
            مثال تطبيقي
          </h4>
          <p className="text-slate-600 text-sm italic">{tips.example}</p>
        </div>
      </CardContent>
    </Card>
  );
};
