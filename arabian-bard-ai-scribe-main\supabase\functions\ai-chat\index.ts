
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatRequest {
  message: string;
  projectType: string;
  writingStyle: string;
  conversationHistory: any[];
  conversationId?: string;
  activeModel?: string;
  userId?: string;
}

// تكوين النماذج المختلفة
const modelConfigs = {
  'gpt-4o-mini': {
    provider: 'openai',
    model: 'gpt-4o-mini',
    maxTokens: 1500,
    temperature: 0.8
  },
  'gpt-4o': {
    provider: 'openai',
    model: 'gpt-4o',
    maxTokens: 2000,
    temperature: 0.7
  },
  'llama-3.1-70b': {
    provider: 'groq',
    model: 'llama-3.1-8b-instant',
    maxTokens: 1500,
    temperature: 0.8
  },
  'claude-3-sonnet': {
    provider: 'anthropic',
    model: 'claude-3-sonnet-20240229',
    maxTokens: 1500,
    temperature: 0.8
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const {
      message,
      projectType,
      writingStyle,
      conversationHistory,
      conversationId,
      activeModel = 'gpt-4o-mini',
      userId
    }: ChatRequest = await req.json()

    // إنشاء عميل Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // الحصول على تكوين النموذج
    const modelConfig = modelConfigs[activeModel] || modelConfigs['gpt-4o-mini']

    // بناء السياق المحسن للنموذج
    const systemPrompt = buildSystemPrompt(projectType, writingStyle, activeModel)

    // إعداد الرسائل
    const messages = [
      { role: "system", content: systemPrompt },
      ...conversationHistory.map((msg: any) => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content
      })),
      { role: "user", content: message }
    ]

    // استدعاء النموذج المناسب
    let aiResponse: string

    switch (modelConfig.provider) {
      case 'openai':
        aiResponse = await callOpenAI(messages, modelConfig)
        break
      case 'groq':
        aiResponse = await callGroq(messages, modelConfig)
        break
      case 'anthropic':
        aiResponse = await callAnthropic(messages, modelConfig)
        break
      default:
        aiResponse = await callOpenAI(messages, modelConfig)
    }

    // حفظ المحادثة في قاعدة البيانات
    if (conversationId && userId) {
      await saveConversation(supabase, conversationId, userId, message, aiResponse, projectType, writingStyle)
    }

    return new Response(
      JSON.stringify({
        response: aiResponse,
        model: activeModel,
        conversationId: conversationId
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  }
})

// بناء السياق المحسن حسب نوع المشروع والنموذج
function buildSystemPrompt(projectType: string, writingStyle: string, activeModel: string): string {
  const basePrompt = `أنت مساعد ذكي متخصص في الكتابة الأدبية العربية. أنت خبير في الأدب العربي ومتمكن من أساليب الكتاب العرب العظماء.

قواعد أساسية يجب اتباعها بدقة:
1. اكتب باللغة العربية الفصحى فقط - لا تستخدم أي كلمات أجنبية
2. اقرأ رسالة المستخدم بعناية وأجب عليها مباشرة
3. كن متسقاً في أسلوبك ولا تتناقض مع نفسك
4. إذا سأل المستخدم سؤالاً، أجب عليه بوضوح ودون إضافات غير مطلوبة
5. إذا طلب كتابة نص، اكتب النص المطلوب فقط
6. لا تخترع خيارات أو اقتراحات لم يطلبها المستخدم
7. كن مفيداً ومباشراً ومفهوماً في إجاباتك
8. حافظ على السياق والاستمرارية في المحادثة

السياق الحالي:
- نوع المشروع: ${projectType}
- أسلوب الكتابة المطلوب: ${writingStyle}
- النموذج المستخدم: ${activeModel}

مهمتك: فهم طلب المستخدم بدقة والإجابة عليه بوضوح ومهنية.`

  const projectSpecificPrompts = {
    'رواية': `
- ساعد في تطوير الشخصيات: خلفياتهم، دوافعهم، صراعاتهم الداخلية
- اقترح تطوير الحبكة: الصراع الرئيسي، العقد، الذروة، الحل
- ساعد في بناء العالم: الزمان، المكان، الجو العام
- اقترح حوارات طبيعية ومعبرة
- ركز على التسلسل المنطقي للأحداث`,

    'قصة قصيرة': `
- ركز على فكرة واحدة قوية ومؤثرة
- ساعد في بناء شخصية محورية مقنعة
- اقترح بداية جذابة ونهاية مؤثرة
- ركز على الكثافة والإيجاز
- ساعد في خلق لحظة تنوير أو تحول`,

    'شعر': `
- ساعد في اختيار البحر الشعري المناسب
- اقترح صور شعرية جميلة ومبتكرة
- ركز على الإيقاع والموسيقى الداخلية
- ساعد في تطوير المعنى والعمق الفكري
- اقترح استخدام البلاغة والمحسنات البديعية`,

    'مقال أدبي': `
- ساعد في بناء الحجة والبرهان
- اقترح أمثلة وشواهد من التراث العربي
- ركز على وضوح الفكرة وقوة التعبير
- ساعد في التنظيم المنطقي للأفكار
- اقترح خاتمة مؤثرة ومقنعة`
  }

  const styleSpecificPrompts = {
    'نجيب محفوظ': `اكتب بأسلوب نجيب محفوظ:

    الخصائص الأسلوبية:
    - الواقعية الاجتماعية: صور الحياة اليومية في مصر بتفاصيلها الدقيقة
    - التحليل النفسي العميق: اغوص في أعماق الشخصيات ودوافعها الداخلية
    - الوصف التفصيلي: صف الأماكن والأشخاص بدقة متناهية (الحارات، البيوت، الوجوه)
    - اللغة البسيطة العميقة: استخدم لغة واضحة لكن محملة بالمعاني
    - الرمزية الاجتماعية: اجعل الأحداث والشخصيات رموزاً لقضايا أكبر
    - التدرج الزمني: اربط الماضي بالحاضر وأظهر تطور الشخصيات

    تقنيات السرد:
    - السرد بضمير الغائب مع التدخل أحياناً
    - الحوار الطبيعي الذي يعكس طبقات المجتمع المختلفة
    - التركيز على التفاصيل الصغيرة التي تكشف الشخصية
    - استخدام الأمثال والحكم الشعبية
    - وصف الحالة النفسية من خلال الأفعال والسلوكيات

    مثال من أسلوبه:
    "كان أحمد عبد الجواد رجلاً في الخامسة والأربعين، طويل القامة، عريض المنكبين، أسمر اللون، كث اللحية، له عينان سوداوان واسعتان، وأنف أقنى، وفم واسع تبدو منه أسنان بيضاء قوية."

    لاحظ: الوصف التفصيلي، البساطة في اللغة، التركيز على التفاصيل الجسدية التي تكشف الشخصية.`,

    'أحمد مراد': `اكتب بأسلوب أحمد مراد:

    الخصائص الأسلوبية:
    - السرد السينمائي: اكتب كأنك تصور فيلماً، بمشاهد متتالية ومؤثرة
    - التشويق والإثارة: ابن التوتر تدريجياً واتركه يتصاعد
    - الحبكة المعقدة: اربط الأحداث بخيوط متشابكة تنكشف تدريجياً
    - الشخصيات المركبة: اجعل كل شخصية لها جوانب متعددة ومتناقضة
    - اللغة العصرية: استخدم لغة الشارع المصري المعاصر مع الفصحى
    - الغموض والكشف: اخف المعلومات ثم اكشفها في اللحظة المناسبة

    تقنيات السرد:
    - التنقل بين الأزمنة والأماكن بسلاسة
    - استخدام الفلاش باك والفلاش فورورد
    - الحوار السريع والحاد الذي يدفع الأحداث
    - وصف الأجواء المشحونة والمواقف المتوترة
    - استخدام التفاصيل التقنية والمعاصرة (التكنولوجيا، وسائل التواصل)

    مثال من أسلوبه:
    "فتح عينيه فجأة. الظلام يلف المكان، والصمت مطبق إلا من صوت تكييف الهواء الذي يعمل بانتظام. نظر إلى ساعة يده المضيئة: الثالثة والنصف فجراً. شيء ما أيقظه، لكنه لا يستطيع تحديد ماهيته."

    لاحظ: البداية المشوقة، السرد السينمائي، التفاصيل الحسية، خلق التوتر من البداية.`,

    'غسان كنفاني': 'ركز على القضايا الاجتماعية والسياسية، الرمزية، والأسلوب الشاعري',
    'أحلام مستغانمي': 'استخدم الرومانسية، اللغة الشاعرية، والعمق العاطفي',
    'طه حسين': 'اعتمد على الأسلوب الأكاديمي، التحليل العميق، والثقافة الواسعة'
  }

  return `${basePrompt}

${projectSpecificPrompts[projectType] || ''}

أسلوب ${writingStyle}: ${styleSpecificPrompts[writingStyle] || ''}

إرشادات مهمة للإجابة:
- استخدم اللغة العربية الفصحى الجميلة فقط
- اقرأ السؤال جيداً وأجب عليه مباشرة
- لا تخترع خيارات أو اقتراحات لم يطلبها المستخدم
- كن واضحاً ومباشراً في إجابتك
- حافظ على الاتساق والمنطق في ردودك
- إذا لم تفهم السؤال، اطلب التوضيح
- احترم الثقافة العربية والإسلامية في اقتراحاتك
- لا تستخدم أي لغة غير العربية مطلقاً`
}

// استدعاء OpenAI API
async function callOpenAI(messages: any[], modelConfig: any): Promise<string> {
  const openaiApiKey = Deno.env.get('OPENAI_API_KEY')

  if (!openaiApiKey) {
    throw new Error('OpenAI API key not found')
  }

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openaiApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: modelConfig.model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    }),
  })

  if (!response.ok) {
    const errorData = await response.text()
    throw new Error(`OpenAI API error: ${response.status} - ${errorData}`)
  }

  const data = await response.json()
  return data.choices[0].message.content
}

// استدعاء Groq API (للنماذج مفتوحة المصدر)
async function callGroq(messages: any[], modelConfig: any): Promise<string> {
  const groqApiKey = Deno.env.get('GROQ_API_KEY')

  if (!groqApiKey) {
    throw new Error('Groq API key not found')
  }

  const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${groqApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: modelConfig.model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    }),
  })

  if (!response.ok) {
    const errorData = await response.text()
    throw new Error(`Groq API error: ${response.status} - ${errorData}`)
  }

  const data = await response.json()
  return data.choices[0].message.content
}

// استدعاء Anthropic API
async function callAnthropic(messages: any[], modelConfig: any): Promise<string> {
  const anthropicApiKey = Deno.env.get('ANTHROPIC_API_KEY')

  if (!anthropicApiKey) {
    throw new Error('Anthropic API key not found')
  }

  // تحويل الرسائل لتنسيق Anthropic
  const systemMessage = messages.find(m => m.role === 'system')?.content || ''
  const conversationMessages = messages.filter(m => m.role !== 'system')

  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'x-api-key': anthropicApiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify({
      model: modelConfig.model,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
      system: systemMessage,
      messages: conversationMessages,
    }),
  })

  if (!response.ok) {
    const errorData = await response.text()
    throw new Error(`Anthropic API error: ${response.status} - ${errorData}`)
  }

  const data = await response.json()
  return data.content[0].text
}

// حفظ المحادثة في قاعدة البيانات
async function saveConversation(
  supabase: any,
  conversationId: string,
  userId: string,
  userMessage: string,
  aiResponse: string,
  projectType: string,
  writingStyle: string
): Promise<void> {
  try {
    // البحث عن المحادثة الموجودة
    const { data: existingConversation, error: fetchError } = await supabase
      .from('conversations')
      .select('messages, title')
      .eq('id', conversationId)
      .eq('user_id', userId)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw fetchError
    }

    const newMessages = [
      ...(existingConversation?.messages || []),
      {
        id: Date.now().toString(),
        type: 'user',
        content: userMessage,
        timestamp: new Date().toISOString()
      },
      {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: aiResponse,
        timestamp: new Date().toISOString()
      }
    ]

    // إنشاء عنوان تلقائي إذا لم يكن موجوداً
    const title = existingConversation?.title || generateConversationTitle(userMessage, projectType)

    if (existingConversation) {
      // تحديث المحادثة الموجودة
      const { error: updateError } = await supabase
        .from('conversations')
        .update({
          messages: newMessages,
          title: title,
          updated_at: new Date().toISOString()
        })
        .eq('id', conversationId)
        .eq('user_id', userId)

      if (updateError) throw updateError
    } else {
      // إنشاء محادثة جديدة
      const { error: insertError } = await supabase
        .from('conversations')
        .insert({
          id: conversationId,
          user_id: userId,
          title: title,
          project_type: projectType,
          writing_style: writingStyle,
          messages: newMessages
        })

      if (insertError) throw insertError
    }
  } catch (error) {
    console.error('Error saving conversation:', error)
    // لا نرمي الخطأ هنا لأن المحادثة يجب أن تستمر حتى لو فشل الحفظ
  }
}

// توليد عنوان تلقائي للمحادثة
function generateConversationTitle(firstMessage: string, projectType: string): string {
  const shortMessage = firstMessage.substring(0, 30)
  const date = new Date().toLocaleDateString('ar-SA')
  return `${projectType} - ${shortMessage}... (${date})`
}
