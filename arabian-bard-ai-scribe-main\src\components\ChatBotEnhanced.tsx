import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { MessageCircle, Send, User, Bot, Copy, Download, Loader2, Save, History, Plus, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { createClient } from '@supabase/supabase-js';

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
}

interface Conversation {
  id: string;
  title: string;
  project_type: string;
  writing_style: string;
  created_at: string;
  updated_at: string;
  messages?: Message[];
}

interface ChatBotEnhancedProps {
  activeModel: string;
}

const supabase = createClient(
  'https://xpbhzuzbnwpvlpgrcijf.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwYmh6dXpibnBwdmxwZ3JjaWpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI3MzE0OTksImV4cCI6MjA0ODMwNzQ5OX0.TgE7YZo7R5PkwmCzgxq1ssMxvGMT1KlZTNnmIQY7ZLI'
);

export const ChatBotEnhanced = ({ activeModel }: ChatBotEnhancedProps) => {
  const { toast } = useToast();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: 'مرحباً بك! أنا مساعدك الذكي لكتابة الأدب العربي المطور. يمكنني مساعدتك في بناء روايات وقصائد خطوة بخطوة مع حفظ محادثاتك. كيف يمكنني مساعدتك اليوم؟',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [projectType, setProjectType] = useState("رواية");
  const [writingStyle, setWritingStyle] = useState("نجيب محفوظ");
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoadingConversations, setIsLoadingConversations] = useState(false);
  const [showConversationsDialog, setShowConversationsDialog] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // معرف المستخدم المؤقت (في التطبيق الحقيقي سيأتي من نظام المصادقة)
  const userId = 'temp-user-id';

  const projectTypes = ["رواية", "قصة قصيرة", "شعر", "مقال أدبي"];
  const writingStyles = ["نجيب محفوظ", "أحمد مراد", "غسان كنفاني", "أحلام مستغانمي", "طه حسين"];

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    loadConversations();
  }, []);

  const loadConversations = async () => {
    setIsLoadingConversations(true);
    try {
      const response = await fetch(`${supabase.supabaseUrl}/functions/v1/conversations?userId=${userId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setConversations(data || []);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل المحادثات",
        variant: "destructive",
      });
    } finally {
      setIsLoadingConversations(false);
    }
  };

  const createNewConversation = async () => {
    try {
      const response = await fetch(`${supabase.supabaseUrl}/functions/v1/conversations`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          projectType,
          writingStyle,
          title: `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentConversationId(data.id);
        setMessages([{
          id: '1',
          type: 'bot',
          content: 'مرحباً بك! أنا مساعدك الذكي لكتابة الأدب العربي المطور. يمكنني مساعدتك في بناء روايات وقصائد خطوة بخطوة مع حفظ محادثاتك. كيف يمكنني مساعدتك اليوم؟',
          timestamp: new Date()
        }]);

        await loadConversations();

        toast({
          title: "تم إنشاء محادثة جديدة",
          description: "يمكنك الآن بدء محادثة جديدة",
        });
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
      toast({
        title: "خطأ",
        description: "فشل في إنشاء محادثة جديدة",
        variant: "destructive",
      });
    }
  };

  const loadConversation = async (conversationId: string) => {
    try {
      const response = await fetch(`${supabase.supabaseUrl}/functions/v1/conversations?userId=${userId}&conversationId=${conversationId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentConversationId(conversationId);
        setMessages(data.messages || []);
        setProjectType(data.project_type);
        setWritingStyle(data.writing_style);
        setShowConversationsDialog(false);

        toast({
          title: "تم تحميل المحادثة",
          description: data.title,
        });
      }
    } catch (error) {
      console.error('Error loading conversation:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل المحادثة",
        variant: "destructive",
      });
    }
  };

  const deleteConversation = async (conversationId: string) => {
    try {
      const response = await fetch(`${supabase.supabaseUrl}/functions/v1/conversations?userId=${userId}&conversationId=${conversationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        if (currentConversationId === conversationId) {
          setCurrentConversationId(null);
          setMessages([{
            id: '1',
            type: 'bot',
            content: 'مرحباً بك! أنا مساعدك الذكي لكتابة الأدب العربي المطور. يمكنني مساعدتك في بناء روايات وقصائد خطوة بخطوة مع حفظ محادثاتك. كيف يمكنني مساعدتك اليوم؟',
            timestamp: new Date()
          }]);
        }

        await loadConversations();

        toast({
          title: "تم حذف المحادثة",
          description: "تم حذف المحادثة بنجاح",
        });
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
      toast({
        title: "خطأ",
        description: "فشل في حذف المحادثة",
        variant: "destructive",
      });
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsTyping(true);

    try {
      console.log('Sending message to AI:', inputMessage);

      const { data, error } = await supabase.functions.invoke('ai-chat', {
        body: {
          message: inputMessage,
          projectType,
          writingStyle,
          conversationHistory: messages,
          conversationId: currentConversationId,
          activeModel,
          userId
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        throw error;
      }

      console.log('AI response received:', data);

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: data.response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);

      // تحديث معرف المحادثة إذا كان جديداً
      if (data.conversationId && !currentConversationId) {
        setCurrentConversationId(data.conversationId);
        await loadConversations();
      }

    } catch (error) {
      console.error('Error calling AI:', error);
      toast({
        title: "خطأ في الاتصال",
        description: "حدث خطأ أثناء التواصل مع الذكاء الاصطناعي. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: 'عذراً، حدث خطأ في التواصل مع النموذج. يرجى المحاولة مرة أخرى.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const exportChat = () => {
    const chatContent = messages.map(msg =>
      `${msg.type === 'user' ? 'أنت' : 'المساعد'}: ${msg.content}`
    ).join('\n\n');

    const blob = new Blob([chatContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `حوار-${projectType}-${new Date().toLocaleDateString('ar')}.txt`;
    a.click();
  };

  const copyLastResponse = () => {
    const lastBotMessage = messages.filter(msg => msg.type === 'bot').pop();
    if (lastBotMessage) {
      navigator.clipboard.writeText(lastBotMessage.content);
      toast({
        title: "تم النسخ",
        description: "تم نسخ آخر رد إلى الحافظة",
      });
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">بوت الدردشة الأدبي المطور</h2>
        <p className="text-slate-600">احترف كتابة الأدب العربي مع حفظ وإدارة محادثاتك</p>
      </div>

      <div className="grid md:grid-cols-4 gap-6">
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 justify-between">
              <div className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5 text-blue-600" />
                محادثة إبداعية
                {currentConversationId && (
                  <Badge variant="outline" className="text-xs">محفوظة</Badge>
                )}
              </div>
              <div className="flex gap-2">
                <Dialog open={showConversationsDialog} onOpenChange={setShowConversationsDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <History className="w-4 h-4 mr-2" />
                      المحادثات
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>المحادثات المحفوظة</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {isLoadingConversations ? (
                        <div className="text-center py-4">
                          <Loader2 className="w-6 h-6 animate-spin mx-auto" />
                          <p className="text-sm text-slate-600 mt-2">جاري تحميل المحادثات...</p>
                        </div>
                      ) : conversations.length === 0 ? (
                        <div className="text-center py-8">
                          <MessageCircle className="w-12 h-12 text-slate-300 mx-auto mb-4" />
                          <p className="text-slate-600">لا توجد محادثات محفوظة</p>
                        </div>
                      ) : (
                        conversations.map((conversation) => (
                          <div key={conversation.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-slate-50">
                            <div className="flex-1 cursor-pointer" onClick={() => loadConversation(conversation.id)}>
                              <h4 className="font-medium text-sm">{conversation.title}</h4>
                              <div className="flex gap-2 mt-1">
                                <Badge variant="outline" className="text-xs">{conversation.project_type}</Badge>
                                <Badge variant="secondary" className="text-xs">{conversation.writing_style}</Badge>
                              </div>
                              <p className="text-xs text-slate-500 mt-1">
                                {new Date(conversation.updated_at).toLocaleDateString('ar-SA')}
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteConversation(conversation.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        ))
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
                <Button variant="outline" size="sm" onClick={createNewConversation}>
                  <Plus className="w-4 h-4 mr-2" />
                  جديدة
                </Button>
              </div>
            </CardTitle>
            <div className="flex gap-2">
              <Badge variant="outline">{activeModel}</Badge>
              <Badge variant="secondary">{projectType}</Badge>
              <Badge variant="outline">{writingStyle}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96 mb-4 p-4 border rounded-lg bg-slate-50" ref={scrollAreaRef}>
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex gap-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        message.type === 'user' ? 'bg-emerald-100' : 'bg-blue-100'
                      }`}>
                        {message.type === 'user' ? (
                          <User className="w-4 h-4 text-emerald-600" />
                        ) : (
                          <Bot className="w-4 h-4 text-blue-600" />
                        )}
                      </div>
                      <div className={`p-3 rounded-lg ${
                        message.type === 'user'
                          ? 'bg-emerald-600 text-white'
                          : 'bg-white border border-slate-200'
                      }`}>
                        <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
                        <span className="text-xs opacity-70 mt-1 block">
                          {message.timestamp.toLocaleTimeString('ar')}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}

                {isTyping && (
                  <div className="flex gap-3 justify-start">
                    <div className="flex gap-2">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center bg-blue-100">
                        <Bot className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="p-3 rounded-lg bg-white border border-slate-200">
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            <div className="flex gap-2">
              <Input
                placeholder="اكتب رسالتك هنا..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && !isTyping && handleSendMessage()}
                className="flex-1"
                disabled={isTyping}
              />
              <Button onClick={handleSendMessage} disabled={!inputMessage.trim() || isTyping}>
                {isTyping ? <Loader2 className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">إعدادات المحادثة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">نوع المشروع</label>
              <Select value={projectType} onValueChange={setProjectType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {projectTypes.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">أسلوب الكتابة</label>
              <Select value={writingStyle} onValueChange={setWritingStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {writingStyles.map((style) => (
                    <SelectItem key={style} value={style}>{style}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="pt-4 space-y-2">
              {currentConversationId && (
                <Button variant="outline" size="sm" className="w-full">
                  <Save className="w-4 h-4 mr-2" />
                  محفوظة تلقائياً
                </Button>
              )}
              <Button variant="outline" size="sm" className="w-full" onClick={exportChat}>
                <Download className="w-4 h-4 mr-2" />
                تصدير المحادثة
              </Button>
              <Button variant="ghost" size="sm" className="w-full" onClick={copyLastResponse}>
                <Copy className="w-4 h-4 mr-2" />
                نسخ آخر رد
              </Button>
            </div>

            <div className="pt-4 border-t">
              <h4 className="font-medium text-sm mb-2">اقتراحات للبدء:</h4>
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("أريد كتابة رواية عن شاب في القاهرة")}
                >
                  رواية عن الحياة في القاهرة
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("ساعدني في كتابة قصيدة عن الوطن")}
                >
                  قصيدة عن حب الوطن
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("كيف أطور شخصيات روايتي؟")}
                >
                  تطوير شخصيات الرواية
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
