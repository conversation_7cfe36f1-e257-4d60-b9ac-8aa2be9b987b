import { useState, useRef, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Send, 
  Bot, 
  User, 
  Loader2, 
  RefreshCw, 
  Settings,
  MessageCircle,
  Sparkles,
  Copy,
  ThumbsUp,
  ThumbsDown
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { modelConfigs, getModelConfig, DEFAULT_MODEL } from "@/config/models";
import { buildRAGEnhancedPrompt } from "@/utils/ragSystem";
import { generateAdvancedLiteraryText } from "@/utils/advancedLiteraryEngine";

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  model?: string;
  liked?: boolean;
}

interface NaturalChatProps {
  initialModel?: string;
}

export const NaturalChat = ({ initialModel = DEFAULT_MODEL }: NaturalChatProps) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'مرحباً! أنا مساعدك الأدبي الذكي. يمكنني مساعدتك في الكتابة بأساليب الكتاب العرب العظماء مثل نجيب محفوظ وأحمد مراد. ما الذي تريد أن نكتب عنه اليوم؟',
      timestamp: new Date()
    }
  ]);
  
  const [currentMessage, setCurrentMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [selectedModel, setSelectedModel] = useState(initialModel);
  const [selectedAuthor, setSelectedAuthor] = useState("نجيب محفوظ");
  const [showSettings, setShowSettings] = useState(false);
  const [isDemo, setIsDemo] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();

  // التحقق من وضع المحاكاة عند التحميل
  useEffect(() => {
    const groqApiKey = import.meta.env.VITE_GROQ_API_KEY;
    setIsDemo(!groqApiKey);
  }, []);

  const authors = [
    { value: "نجيب محفوظ", label: "نجيب محفوظ", emoji: "📚" },
    { value: "أحمد مراد", label: "أحمد مراد", emoji: "🔍" },
    { value: "عام", label: "أسلوب عام", emoji: "✍️" }
  ];

  // التمرير التلقائي للأسفل
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // دالة إرسال الرسالة
  const sendMessage = async () => {
    if (!currentMessage.trim() || isTyping) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: currentMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setCurrentMessage("");
    setIsTyping(true);

    try {
      // بناء السياق للمحادثة
      const conversationHistory = messages.slice(-5).map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const response = await generateResponse(userMessage.content, conversationHistory);
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response,
        timestamp: new Date(),
        model: selectedModel
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error) {
      console.error('Error generating response:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'عذراً، حدث خطأ في توليد الرد. يرجى المحاولة مرة أخرى.',
        timestamp: new Date(),
        model: selectedModel
      };

      setMessages(prev => [...prev, errorMessage]);
      
      toast({
        title: "خطأ في الاتصال",
        description: "حدث خطأ أثناء توليد الرد",
        variant: "destructive",
      });
    } finally {
      setIsTyping(false);
    }
  };

  // دالة توليد الرد
  const generateResponse = async (userInput: string, history: any[]) => {
    const modelConfig = getModelConfig(selectedModel);
    if (!modelConfig) {
      throw new Error('تكوين النموذج غير موجود');
    }

    // بناء prompt محسن للدردشة
    const systemPrompt = buildChatPrompt(selectedAuthor, history);

    // التحقق من وجود API key
    const groqApiKey = import.meta.env.VITE_GROQ_API_KEY;

    // إذا لم يوجد API key، استخدم الردود المحاكاة
    if (!groqApiKey) {
      return generateMockResponse(userInput, selectedAuthor);
    }

    // استخدام Groq API للنماذج المجانية
    if (modelConfig.provider === 'groq') {
      return await generateWithGroq(systemPrompt, userInput, modelConfig);
    }

    // يمكن إضافة مزودين آخرين هنا
    throw new Error('مزود النموذج غير مدعوم حالياً');
  };

  // دالة بناء prompt للدردشة
  const buildChatPrompt = (author: string, history: any[]) => {
    const systemPrompt = `أنت مساعد أدبي ذكي متخصص في الكتابة العربية. تتحدث بطريقة طبيعية ومفيدة.

خصائصك:
• تساعد في الكتابة بأساليب الكتاب العرب العظماء
• تقدم نصائح أدبية مفيدة وعملية
• تتحدث بطريقة ودودة وطبيعية
• تحافظ على استمرارية المحادثة
• تستخدم اللغة العربية الفصحى بطريقة مبسطة

الأسلوب المطلوب حالياً: ${author}

${author === "نجيب محفوظ" ? `
تقنيات نجيب محفوظ:
• الوصف التفصيلي للشخصيات والأماكن
• التحليل النفسي العميق
• استخدام الرمزية الاجتماعية
• ربط الأحداث الشخصية بالتغيرات الكبيرة
• المفردات: الحارة، الحوش، المشربية، الدكة
` : author === "أحمد مراد" ? `
تقنيات أحمد مراد:
• البدايات المشوقة والمفاجئة
• خلق أجواء التوتر والإثارة
• الحوار السريع والحاد
• الغموض والكشف التدريجي
• المفردات: التوتر، القلق، المؤامرة، الإثارة
` : `
أسلوب عام متوازن يجمع بين الوضوح والجمال الأدبي.
`}

تعليمات المحادثة:
• اجعل ردودك طبيعية ومتدفقة
• لا تكرر نفس العبارات
• اربط بين الرسائل السابقة
• قدم أمثلة عملية عند الحاجة
• اسأل أسئلة لفهم احتياجات المستخدم بشكل أفضل`;

    return systemPrompt;
  };

  // دالة توليد الردود المحاكاة المتقدمة (عندما لا يوجد API key)
  const generateMockResponse = (userInput: string, author: string) => {
    // استخدام المحرك الأدبي المتقدم
    const literaryResult = generateAdvancedLiteraryText(
      author,
      userInput,
      'auto',
      'medium'
    );

    // إنشاء رد تفاعلي يتضمن النص الأدبي والتحليل
    let response = "";

    // رد تفاعلي حسب نوع الطلب
    if (userInput.includes('قصة') || userInput.includes('حكاية')) {
      response = `ممتاز! دعني أكتب لك قصة بأسلوب ${author}:\n\n"${literaryResult.text}"\n\n`;
      response += `💡 **التقنيات المستخدمة:** ${literaryResult.techniques.join('، ')}\n\n`;
      response += `📊 **التحليل:** ${literaryResult.analysis}\n\n`;
      response += `⭐ **جودة النص:** ${literaryResult.quality.toFixed(1)}/10`;
    }
    else if (userInput.includes('شخصية') || userInput.includes('وصف')) {
      response = `بالطبع! إليك مثال على وصف شخصية بأسلوب ${author}:\n\n"${literaryResult.text}"\n\n`;
      response += `🎨 **لاحظ كيف:** ${literaryResult.analysis}\n\n`;
      response += `📝 **التقنيات:** ${literaryResult.techniques.join('، ')}\n\n`;
      response += `هل تريد أن نطور هذه الشخصية أكثر؟`;
    }
    else if (userInput.includes('حوار') || userInput.includes('كلام')) {
      response = `ممتاز! إليك مثال على حوار بأسلوب ${author}:\n\n"${literaryResult.text}"\n\n`;
      response += `🎭 **تحليل الحوار:** ${literaryResult.analysis}\n\n`;
      response += `⚡ **التقنيات:** ${literaryResult.techniques.join('، ')}\n\n`;
      response += `هل تريد أن نكمل هذا الحوار؟`;
    }
    else if (userInput.includes('بداية') || userInput.includes('فجأة')) {
      response = `رائع! إليك بداية مشوقة بأسلوب ${author}:\n\n"${literaryResult.text}"\n\n`;
      response += `🚀 **سر القوة:** ${literaryResult.analysis}\n\n`;
      response += `🔥 **التقنيات:** ${literaryResult.techniques.join('، ')}\n\n`;
      response += `هل تريد أن نكمل هذه القصة؟`;
    }
    else {
      // رد عام مع نصائح
      const tips = {
        "نجيب محفوظ": [
          "ابدأ بوصف المكان والزمان بتفصيل دقيق",
          "اربط الأحداث الشخصية بالتغيرات الاجتماعية",
          "استخدم الحوار ليعكس الطبقة الاجتماعية للشخصيات",
          "وظف الرمزية لإضافة عمق للمعنى"
        ],
        "أحمد مراد": [
          "ابدأ بمشهد مفاجئ يجذب القارئ فوراً",
          "اخلق جو من التوتر والغموض",
          "استخدم الحوار السريع لدفع الأحداث",
          "اكشف المعلومات تدريجياً لزيادة التشويق"
        ],
        "عام": [
          "اقرأ كثيراً في الأدب العربي الكلاسيكي والمعاصر",
          "مارس الكتابة يومياً ولو لدقائق قليلة",
          "طور مفردatك وأسلوبك الخاص",
          "اهتم بالتفاصيل الصغيرة التي تصنع الفرق"
        ]
      };

      const authorTips = tips[author] || tips["عام"];
      const randomTip = authorTips[Math.floor(Math.random() * authorTips.length)];

      response = `أهلاً بك! أنا هنا لمساعدتك في تطوير مهاراتك الأدبية بأسلوب ${author}.\n\n`;
      response += `💡 **نصيحة اليوم:** ${randomTip}\n\n`;
      response += `إليك مثال على الكتابة بهذا الأسلوب:\n\n"${literaryResult.text}"\n\n`;
      response += `📚 **ما يميز هذا النص:** ${literaryResult.analysis}\n\n`;
      response += `ما نوع النص الذي تريد أن نعمل عليه معاً؟ (قصة، وصف شخصية، حوار، بداية مشوقة...)`;
    }

    return response;
  };

  // دالة التوليد باستخدام Groq
  const generateWithGroq = async (systemPrompt: string, userInput: string, modelConfig: any) => {
    const groqApiKey = import.meta.env.VITE_GROQ_API_KEY;
    
    if (!groqApiKey) {
      throw new Error('مفتاح Groq API غير موجود');
    }

    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${groqApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: modelConfig.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userInput }
        ],
        max_tokens: modelConfig.maxTokens,
        temperature: modelConfig.temperature,
        stream: false
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`خطأ في Groq API: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  };

  // دالة مسح المحادثة
  const clearChat = () => {
    setMessages([
      {
        id: '1',
        role: 'assistant',
        content: 'تم مسح المحادثة. كيف يمكنني مساعدتك اليوم؟',
        timestamp: new Date()
      }
    ]);
  };

  // دالة نسخ الرسالة
  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "تم النسخ",
      description: "تم نسخ الرسالة إلى الحافظة",
    });
  };

  // دالة تقييم الرسالة
  const rateMessage = (messageId: string, liked: boolean) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, liked } : msg
    ));
    
    toast({
      title: liked ? "شكراً لك!" : "تم التسجيل",
      description: liked ? "سنحرص على تحسين الردود" : "سنعمل على تحسين هذا النوع من الردود",
    });
  };

  // التعامل مع Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="flex flex-col h-[80vh] max-w-4xl mx-auto">
      {/* Header */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5 text-blue-600" />
              الدردشة الأدبية الطبيعية
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearChat}
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          {isDemo && (
            <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center gap-2 text-amber-800">
                <Sparkles className="w-4 h-4" />
                <span className="text-sm font-medium">وضع المحاكاة</span>
              </div>
              <p className="text-xs text-amber-700 mt-1">
                يتم استخدام ردود محاكاة ذكية. لاستخدام النماذج الحقيقية، أضف مفتاح Groq API.
              </p>
            </div>
          )}

          {showSettings && (
            <div className="grid grid-cols-2 gap-4 mt-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium mb-2">النموذج:</label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {modelConfigs.map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        <div className="flex items-center gap-2">
                          <span>{model.name}</span>
                          <Badge variant={model.cost === 'low' ? 'default' : 'secondary'}>
                            {model.cost === 'low' ? 'مجاني' : model.cost}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">الأسلوب الأدبي:</label>
                <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {authors.map((author) => (
                      <SelectItem key={author.value} value={author.value}>
                        <span>{author.emoji} {author.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Messages */}
      <Card className="flex-1 flex flex-col">
        <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.role === 'assistant' && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-blue-100 text-blue-600">
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )}
              
              <div className={`max-w-[70%] ${message.role === 'user' ? 'order-first' : ''}`}>
                <div
                  className={`p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                </div>
                
                <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                  <span>{message.timestamp.toLocaleTimeString('ar')}</span>
                  {message.model && (
                    <Badge variant="outline" className="text-xs">
                      {getModelConfig(message.model)?.name}
                    </Badge>
                  )}
                  
                  {message.role === 'assistant' && (
                    <div className="flex items-center gap-1 mr-auto">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => copyMessage(message.content)}
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-6 w-6 p-0 ${message.liked === true ? 'text-green-600' : ''}`}
                        onClick={() => rateMessage(message.id, true)}
                      >
                        <ThumbsUp className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-6 w-6 p-0 ${message.liked === false ? 'text-red-600' : ''}`}
                        onClick={() => rateMessage(message.id, false)}
                      >
                        <ThumbsDown className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              
              {message.role === 'user' && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-green-100 text-green-600">
                    <User className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          
          {isTyping && (
            <div className="flex gap-3 justify-start">
              <Avatar className="w-8 h-8">
                <AvatarFallback className="bg-blue-100 text-blue-600">
                  <Bot className="w-4 h-4" />
                </AvatarFallback>
              </Avatar>
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-gray-600">يكتب...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </CardContent>

        {/* Input */}
        <div className="p-4 border-t">
          <div className="flex gap-2">
            <Textarea
              ref={textareaRef}
              value={currentMessage}
              onChange={(e) => setCurrentMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="اكتب رسالتك هنا... (اضغط Enter للإرسال)"
              className="min-h-[60px] max-h-[120px] resize-none"
              disabled={isTyping}
            />
            <Button
              onClick={sendMessage}
              disabled={!currentMessage.trim() || isTyping}
              className="self-end"
            >
              {isTyping ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
          
          <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
            <span>
              النموذج النشط: {getModelConfig(selectedModel)?.name} • 
              الأسلوب: {authors.find(a => a.value === selectedAuthor)?.label}
            </span>
            <span>Shift + Enter للسطر الجديد</span>
          </div>
        </div>
      </Card>
    </div>
  );
};
