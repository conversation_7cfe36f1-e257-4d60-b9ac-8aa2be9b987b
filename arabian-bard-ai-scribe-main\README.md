# نموذج الذكاء الاصطناعي الأدبي العربي

منصة متطورة لتطوير وتدريب نماذج الذكاء الاصطناعي المتخصصة في الأدب العربي باستخدام أحدث تقنيات التعلم الآلي والنماذج مفتوحة المصدر.

## 🌟 الميزات الرئيسية

### 1. بوت الدردشة الأدبي المطور
- دعم نماذج متعددة (GPT-4, Claude, <PERSON>lama)
- حفظ وإدارة المحادثات
- أساليب كتابة متنوعة (نجيب محفوظ، أحمد مراد، غسان كنفاني، إلخ)
- تخصيص حسب نوع المشروع (رواية، شعر، مقال)

### 2. واجهة الكتابة الإبداعية المحسنة
- توليد محتوى ذكي بأساليب مختلفة
- دعم أنواع محتوى متعددة (قصة، شعر، حوار، وصف)
- تحكم في طول المحتوى
- تصدير ونسخ النصوص

### 3. ورشة الروايات الطويلة
- كتابة روايات تصل إلى 100,000 كلمة
- تنظيم الفصول والشخصيات
- مساعد ذكي لتطوير الحبكة
- إحصائيات مفصلة للتقدم

### 4. نظام إدارة النماذج
- دعم OpenAI (GPT-4, GPT-4o-mini)
- دعم Anthropic (Claude)
- دعم Groq (Llama 3.1)
- تبديل سلس بين النماذج

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** مع TypeScript
- **Vite** كأداة البناء
- **Tailwind CSS** للتصميم
- **shadcn/ui** لمكونات الواجهة
- **React Router** للتنقل

### Backend
- **Supabase** لقاعدة البيانات والمصادقة
- **Edge Functions** للمعالجة الخلفية
- **PostgreSQL** لتخزين البيانات

### AI Integration
- **OpenAI API** (GPT-4, GPT-4o-mini)
- **Anthropic API** (Claude)
- **Groq API** (Llama 3.1)

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js 18+ و npm
- حساب Supabase
- مفاتيح API للنماذج المطلوبة

### خطوات التثبيت

```bash
# 1. استنساخ المشروع
git clone <YOUR_GIT_URL>
cd arabian-bard-ai-scribe

# 2. تثبيت التبعيات
npm install

# 3. إعداد متغيرات البيئة
# أنشئ ملف .env.local وأضف:
# VITE_SUPABASE_URL=your_supabase_url
# VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# 4. تشغيل التطبيق
npm run dev
```

### إعداد Supabase

1. أنشئ مشروع جديد في [Supabase](https://supabase.com)
2. شغل migration لإنشاء الجداول:
```sql
-- تشغيل ملف supabase/migrations/20241127000001_create_conversations.sql
```

3. رفع Edge Functions:
```bash
# تثبيت Supabase CLI
npm install -g @supabase/cli

# رفع Functions
supabase functions deploy ai-chat
supabase functions deploy conversations
supabase functions deploy generate-content
```

4. إعداد متغيرات البيئة في Supabase:
```bash
supabase secrets set OPENAI_API_KEY=your_openai_key
supabase secrets set GROQ_API_KEY=your_groq_key
supabase secrets set ANTHROPIC_API_KEY=your_anthropic_key
```

## 📁 هيكل المشروع

```
src/
├── components/
│   ├── ChatBot.tsx              # بوت الدردشة الأساسي
│   ├── ChatBotEnhanced.tsx      # بوت الدردشة المطور
│   ├── WritingInterface.tsx     # واجهة الكتابة الإبداعية
│   ├── ModelSelection.tsx       # اختيار النماذج
│   ├── DataPreparation.tsx      # إعداد البيانات
│   ├── TrainingPanel.tsx        # لوحة التدريب
│   └── ui/                      # مكونات الواجهة
├── pages/
│   ├── Index.tsx               # الصفحة الرئيسية
│   ├── NovelWriter.tsx         # ورشة الروايات
│   └── NotFound.tsx            # صفحة 404
└── hooks/                      # React Hooks

supabase/
├── functions/
│   ├── ai-chat/                # دالة الدردشة الذكية
│   ├── conversations/          # إدارة المحادثات
│   └── generate-content/       # توليد المحتوى
└── migrations/                 # ملفات قاعدة البيانات
```

## 🎯 كيفية الاستخدام

### 1. الدردشة الأدبية
1. اختر نوع المشروع (رواية، شعر، مقال)
2. حدد أسلوب الكتابة المفضل
3. ابدأ المحادثة مع المساعد الذكي
4. احفظ المحادثات المهمة للرجوع إليها

### 2. الكتابة الإبداعية
1. اكتب موضوعك أو فكرتك
2. اختر نوع المحتوى المطلوب
3. حدد الأسلوب وطول النص
4. اضغط "إنتاج النص" للحصول على محتوى مخصص

### 3. كتابة الروايات
1. انتقل إلى "ورشة الروايات الطويلة"
2. أنشئ مشروع رواية جديد
3. استخدم المساعد الذكي لتطوير الفصول
4. تابع تقدمك من خلال الإحصائيات

## 🔧 التخصيص والتطوير

### إضافة نموذج جديد
1. أضف تكوين النموذج في `modelConfigs`
2. أنشئ دالة استدعاء API جديدة
3. أضف النموذج إلى قائمة الاختيار

### إضافة أسلوب كتابة جديد
1. أضف الأسلوب إلى قائمة `writingStyles`
2. أضف وصف الأسلوب في `styleSpecificPrompts`
3. اختبر النتائج وحسن التوجيهات

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🔗 روابط مفيدة

- [Supabase Documentation](https://supabase.com/docs)
- [OpenAI API](https://platform.openai.com/docs)
- [Anthropic API](https://docs.anthropic.com)
- [Groq API](https://console.groq.com/docs)
- [React Documentation](https://react.dev)
- [Tailwind CSS](https://tailwindcss.com)
