import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, Star, Zap, Globe, Code, Heart, AlertCircle, ExternalLink } from "lucide-react";

interface OpenSourceModel {
  id: string;
  name: string;
  provider: string;
  size: string;
  description: string;
  arabicSupport: 'excellent' | 'good' | 'basic';
  specialties: string[];
  availability: 'groq' | 'huggingface' | 'ollama' | 'api';
  cost: 'free' | 'low' | 'medium';
  speed: 'fast' | 'medium' | 'slow';
  recommended: boolean;
  pros: string[];
  cons: string[];
  setupInstructions?: string;
}

const openSourceModels: OpenSourceModel[] = [
  {
    id: 'mistral-saba-24b',
    name: 'Mistral Saba 24B',
    provider: 'Mistral AI',
    size: '24B',
    description: 'نموذج متخصص في العربية والفارسية (يحتاج موافقة على الشروط في Groq)',
    arabicSupport: 'excellent',
    specialties: ['الكتابة الأدبية', 'الشعر العربي', 'النثر الكلاسيكي', 'اللهجات العربية'],
    availability: 'groq',
    cost: 'free',
    speed: 'fast',
    recommended: false,
    pros: [
      'متخصص في العربية',
      'سريع جداً على Groq',
      'مجاني تماماً',
      'دعم ممتاز للشعر والأدب'
    ],
    cons: [
      'حجم أصغر من النماذج الكبيرة',
      'قد يحتاج توجيهات مفصلة'
    ],
    setupInstructions: 'متاح مباشرة على Groq API'
  },
  {
    id: 'llama-3.3-70b',
    name: 'Llama 3.3 70B',
    provider: 'Meta',
    size: '70B',
    description: 'أحدث نموذج من Meta بدعم محسن للغات متعددة منها العربية',
    arabicSupport: 'excellent',
    specialties: ['الروايات الطويلة', 'التحليل الأدبي', 'الكتابة الإبداعية', 'الترجمة'],
    availability: 'groq',
    cost: 'free',
    speed: 'medium',
    recommended: true,
    pros: [
      'جودة عالية جداً',
      'فهم عميق للسياق',
      'ممتاز للنصوص الطويلة',
      'مجاني على Groq'
    ],
    cons: [
      'أبطأ من النماذج الصغيرة',
      'قد يحتاج وقت انتظار'
    ]
  },
  {
    id: 'jais-30b',
    name: 'JAIS 30B',
    provider: 'G42 (الإمارات)',
    size: '30B',
    description: 'أول نموذج عربي متخصص من الإمارات، مصمم خصيصاً للثقافة العربية',
    arabicSupport: 'excellent',
    specialties: ['الأدب العربي الكلاسيكي', 'الثقافة العربية', 'التراث', 'الشعر النبطي'],
    availability: 'huggingface',
    cost: 'free',
    speed: 'medium',
    recommended: true,
    pros: [
      'مصمم للثقافة العربية',
      'فهم عميق للتراث',
      'ممتاز للشعر العربي',
      'مفتوح المصدر بالكامل'
    ],
    cons: [
      'يحتاج إعداد محلي',
      'استهلاك ذاكرة عالي',
      'ليس متاح على Groq'
    ],
    setupInstructions: 'يحتاج تحميل من Hugging Face وتشغيل محلي'
  },
  {
    id: 'qwen-2.5-32b',
    name: 'Qwen 2.5 32B',
    provider: 'Alibaba',
    size: '32B',
    description: 'نموذج صيني بدعم ممتاز للعربية والإنجليزية',
    arabicSupport: 'good',
    specialties: ['الكتابة التقنية', 'الترجمة', 'المحتوى المختلط', 'التحليل'],
    availability: 'groq',
    cost: 'free',
    speed: 'fast',
    recommended: false,
    pros: [
      'سريع ومجاني',
      'جيد للمحتوى التقني',
      'دعم متعدد اللغات'
    ],
    cons: [
      'أقل تخصصاً في الأدب العربي',
      'قد يخلط بين اللغات'
    ]
  },
  {
    id: 'llama-3.1-8b',
    name: 'Llama 3.1 8B Instant',
    provider: 'Meta',
    size: '8B',
    description: 'نموذج سريع ومجاني للاستخدام العام',
    arabicSupport: 'good',
    specialties: ['المحادثات', 'النصوص القصيرة', 'التجارب السريعة'],
    availability: 'groq',
    cost: 'free',
    speed: 'fast',
    recommended: false,
    pros: [
      'سريع جداً',
      'مجاني تماماً',
      'استهلاك موارد قليل'
    ],
    cons: [
      'جودة أقل للنصوص المعقدة',
      'محدود في الإبداع الأدبي'
    ]
  }
];

interface OpenSourceModelSelectorProps {
  activeModel: string;
  setActiveModel: (model: string) => void;
}

export const OpenSourceModelSelector = ({ activeModel, setActiveModel }: OpenSourceModelSelectorProps) => {
  const [selectedCategory, setSelectedCategory] = useState('recommended');

  const getArabicSupportBadge = (level: string) => {
    switch (level) {
      case 'excellent': return <Badge className="bg-green-100 text-green-800">ممتاز للعربية</Badge>;
      case 'good': return <Badge className="bg-yellow-100 text-yellow-800">جيد للعربية</Badge>;
      case 'basic': return <Badge className="bg-red-100 text-red-800">دعم أساسي</Badge>;
      default: return null;
    }
  };

  const getAvailabilityIcon = (availability: string) => {
    switch (availability) {
      case 'groq': return <Zap className="w-4 h-4 text-purple-500" />;
      case 'huggingface': return <Code className="w-4 h-4 text-orange-500" />;
      case 'ollama': return <Globe className="w-4 h-4 text-blue-500" />;
      case 'api': return <ExternalLink className="w-4 h-4 text-gray-500" />;
      default: return null;
    }
  };

  const filteredModels = selectedCategory === 'recommended' 
    ? openSourceModels.filter(m => m.recommended)
    : selectedCategory === 'arabic-specialized'
    ? openSourceModels.filter(m => m.arabicSupport === 'excellent')
    : openSourceModels;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">النماذج مفتوحة المصدر للعربية</h2>
        <p className="text-slate-600">
          اختر من أفضل النماذج المجانية والمتخصصة في اللغة العربية
        </p>
      </div>

      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="recommended">الموصى بها</TabsTrigger>
          <TabsTrigger value="arabic-specialized">متخصصة في العربية</TabsTrigger>
          <TabsTrigger value="all">جميع النماذج</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedCategory} className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            {filteredModels.map((model) => (
              <Card 
                key={model.id} 
                className={`relative transition-all duration-300 ${
                  activeModel === model.id 
                    ? 'ring-2 ring-emerald-500 shadow-lg' 
                    : 'hover:shadow-md'
                }`}
              >
                {model.recommended && (
                  <Badge className="absolute -top-2 right-4 bg-emerald-500 hover:bg-emerald-600">
                    <Star className="w-3 h-3 mr-1" />
                    موصى به
                  </Badge>
                )}
                
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2">
                    {getAvailabilityIcon(model.availability)}
                    {model.name}
                    <Badge variant="outline">{model.size}</Badge>
                  </CardTitle>
                  <CardDescription>{model.description}</CardDescription>
                  
                  <div className="flex gap-2 mt-2">
                    {getArabicSupportBadge(model.arabicSupport)}
                    <Badge className="bg-blue-100 text-blue-800">
                      {model.cost === 'free' ? 'مجاني' : 'مدفوع'}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-3">
                  <div>
                    <h4 className="font-semibold text-sm mb-2">التخصصات:</h4>
                    <div className="flex flex-wrap gap-1">
                      {model.specialties.slice(0, 3).map((specialty, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="font-semibold text-green-700">المزايا:</span>
                      <ul className="mt-1 space-y-1">
                        {model.pros.slice(0, 2).map((pro, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <CheckCircle className="w-3 h-3 text-green-500" />
                            <span className="text-green-700">{pro}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <span className="font-semibold text-orange-700">التحديات:</span>
                      <ul className="mt-1 space-y-1">
                        {model.cons.slice(0, 2).map((con, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <AlertCircle className="w-3 h-3 text-orange-500" />
                            <span className="text-orange-700">{con}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <Button 
                    className={`w-full ${
                      activeModel === model.id 
                        ? 'bg-emerald-600 hover:bg-emerald-700' 
                        : 'bg-slate-600 hover:bg-slate-700'
                    }`}
                    onClick={() => setActiveModel(model.id)}
                  >
                    {activeModel === model.id ? 'مُحدد حالياً' : 'اختيار هذا النموذج'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* معلومات النموذج المحدد */}
      {activeModel && (
        <Card className="bg-emerald-50 border-emerald-200">
          <CardHeader>
            <CardTitle className="text-emerald-800 flex items-center gap-2">
              <Heart className="w-5 h-5" />
              النموذج المحدد: {openSourceModels.find(m => m.id === activeModel)?.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4 text-sm">
              <div>
                <h4 className="font-semibold text-emerald-800 mb-2">المزايا:</h4>
                <ul className="space-y-1">
                  {openSourceModels.find(m => m.id === activeModel)?.pros.map((pro, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="w-3 h-3 text-emerald-600" />
                      <span className="text-emerald-700">{pro}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-emerald-800 mb-2">التخصصات:</h4>
                <ul className="space-y-1">
                  {openSourceModels.find(m => m.id === activeModel)?.specialties.map((specialty, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <Star className="w-3 h-3 text-emerald-600" />
                      <span className="text-emerald-700">{specialty}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-emerald-800 mb-2">معلومات تقنية:</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Globe className="w-3 h-3 text-emerald-600" />
                    <span className="text-emerald-700">
                      المزود: {openSourceModels.find(m => m.id === activeModel)?.provider}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Zap className="w-3 h-3 text-emerald-600" />
                    <span className="text-emerald-700">
                      الحجم: {openSourceModels.find(m => m.id === activeModel)?.size}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
