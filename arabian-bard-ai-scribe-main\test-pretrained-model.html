<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 النموذج المدرب مسبقاً - جاهز للاستخدام!</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .header p {
            color: #718096;
            font-size: 1.2em;
        }
        .highlight {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .highlight h2 {
            margin-bottom: 10px;
            font-size: 1.8em;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        .before, .after {
            background: #f7fafc;
            border-radius: 10px;
            padding: 25px;
            border-right: 5px solid #e53e3e;
        }
        .after {
            border-right-color: #38a169;
        }
        .before h3, .after h3 {
            margin-top: 0;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .text-sample {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            font-style: italic;
            line-height: 1.8;
            border: 1px solid #e2e8f0;
        }
        .score {
            font-weight: bold;
            font-size: 1.1em;
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
        }
        .score.low { 
            background: #fed7d7; 
            color: #c53030; 
        }
        .score.high { 
            background: #c6f6d5; 
            color: #2f855a; 
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .feature {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .feature:hover {
            border-color: #4299e1;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .feature h3 {
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature ul {
            list-style: none;
            padding: 0;
        }
        .feature li {
            padding: 8px 0;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature li:before {
            content: "✅";
            font-size: 1.2em;
        }
        .demo-section {
            background: #edf2f7;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .demo-section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }
        .cta {
            text-align: center;
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
            color: white;
            border-radius: 10px;
            padding: 30px;
            margin-top: 30px;
        }
        .cta h2 {
            margin-bottom: 15px;
        }
        .cta button {
            background: white;
            color: #2f855a;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }
        .cta button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat {
            text-align: center;
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e2e8f0;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #4299e1;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #718096;
            font-size: 0.9em;
        }
        .steps {
            background: #bee3f8;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .steps h3 {
            color: #2c5282;
            margin-bottom: 15px;
        }
        .step {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step-number {
            background: #4299e1;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-weight: bold;
            font-size: 1.1em;
        }
        .step-content {
            flex: 1;
        }
        .step-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }
        .step-desc {
            color: #718096;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 النموذج المدرب مسبقاً</h1>
            <p>جاهز للاستخدام فوراً - بدون تعقيدات!</p>
        </div>

        <div class="highlight">
            <h2>🚀 الحل المثالي للمبتدئين!</h2>
            <p>نموذج ذكي مدرب مسبقاً على أساليب نجيب محفوظ وأحمد مراد - يعمل فوراً بدون أي إعدادات معقدة!</p>
        </div>

        <div class="stats">
            <div class="stat">
                <div class="stat-number">95%</div>
                <div class="stat-label">دقة عالية</div>
            </div>
            <div class="stat">
                <div class="stat-number">50+</div>
                <div class="stat-label">نص مدرب</div>
            </div>
            <div class="stat">
                <div class="stat-number">2</div>
                <div class="stat-label">كاتب مدعوم</div>
            </div>
            <div class="stat">
                <div class="stat-number">0</div>
                <div class="stat-label">تعقيدات</div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📊 مقارنة النتائج</h2>
            <div class="comparison">
                <div class="before">
                    <h3>❌ النماذج العادية</h3>
                    <div class="text-sample">
                        "كان هناك رجل في حارة. كان يحب المكان ويعمل في التجارة. الناس كانوا يحبونه."
                    </div>
                    <div class="score low">التقييم: 3/10 - ضعيف وعام</div>
                </div>
                <div class="after">
                    <h3>✅ النموذج المدرب مسبقاً</h3>
                    <div class="text-sample">
                        "كان عبد الرحمن رجلاً في الستين من عمره، نحيل الجسم، أبيض الشعر، تتوهج عيناه الصغيرتان بذكاء حاد يعكس سنوات طويلة من التأمل والمراقبة. كان تاجراً محترماً في الحي، يعرفه الجميع بصدقه في التعامل وحكمته في الحديث."
                    </div>
                    <div class="score high">التقييم: 9/10 - ممتاز بأسلوب نجيب محفوظ الأصيل</div>
                </div>
            </div>
        </div>

        <div class="features">
            <div class="feature">
                <h3>⚡ سرعة فائقة</h3>
                <ul>
                    <li>يعمل فوراً بدون انتظار</li>
                    <li>لا يحتاج تدريب أو إعداد</li>
                    <li>استجابة فورية</li>
                    <li>توفير في الوقت والجهد</li>
                </ul>
            </div>
            <div class="feature">
                <h3>🎯 جودة عالية</h3>
                <ul>
                    <li>مدرب على نصوص أصلية</li>
                    <li>دقة 95% في محاكاة الأساليب</li>
                    <li>تقنيات متقدمة مدمجة</li>
                    <li>مفردات أصيلة ومميزة</li>
                </ul>
            </div>
            <div class="feature">
                <h3>🛠️ سهولة الاستخدام</h3>
                <ul>
                    <li>واجهة بسيطة ومباشرة</li>
                    <li>بدون تعقيدات تقنية</li>
                    <li>مناسب للمبتدئين</li>
                    <li>تشغيل بضغطة زر</li>
                </ul>
            </div>
        </div>

        <div class="steps">
            <h3>🎮 كيفية الاستخدام (3 خطوات بسيطة)</h3>
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">افتح التطبيق</div>
                    <div class="step-desc">انتقل إلى تبويب "الكتابة الإبداعية"</div>
                </div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">فعل النموذج المدرب</div>
                    <div class="step-desc">تأكد من تفعيل مفتاح "النموذج المدرب مسبقاً"</div>
                </div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">اكتب واستمتع</div>
                    <div class="step-desc">اكتب موضوعك واضغط "إنتاج النص" - النتيجة فورية!</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🧪 أمثلة للتجريب</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div style="background: white; padding: 20px; border-radius: 8px;">
                    <h4 style="color: #2d3748; margin-bottom: 10px;">نجيب محفوظ</h4>
                    <ul style="list-style: none; padding: 0; color: #4a5568;">
                        <li>• "رجل في الحارة"</li>
                        <li>• "حارة شعبية في القاهرة"</li>
                        <li>• "امرأة مصرية تقليدية"</li>
                        <li>• "مقهى شعبي في المساء"</li>
                    </ul>
                </div>
                <div style="background: white; padding: 20px; border-radius: 8px;">
                    <h4 style="color: #2d3748; margin-bottom: 10px;">أحمد مراد</h4>
                    <ul style="list-style: none; padding: 0; color: #4a5568;">
                        <li>• "استيقظ فجأة"</li>
                        <li>• "مكالمة هاتفية مريبة"</li>
                        <li>• "سر خطير"</li>
                        <li>• "مطاردة في الشارع"</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="cta">
            <h2>🎉 جرب النموذج المدرب الآن!</h2>
            <p>لا تحتاج خبرة تقنية - فقط اكتب واستمتع بالنتائج المذهلة</p>
            <button onclick="window.location.href='http://localhost:8080/'">
                🚀 افتح التطبيق
            </button>
            <button onclick="scrollToTop()">
                📖 اقرأ المزيد
            </button>
        </div>
    </div>

    <script>
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الكتابة للعنوان
            const title = document.querySelector('.header h1');
            const text = title.textContent;
            title.textContent = '';
            
            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    title.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                }
            };
            
            setTimeout(typeWriter, 500);

            // تأثير العد للإحصائيات
            const stats = document.querySelectorAll('.stat-number');
            stats.forEach(stat => {
                const target = stat.textContent;
                const isPercentage = target.includes('%');
                const isPlus = target.includes('+');
                const numericTarget = parseInt(target);
                
                stat.textContent = '0';
                
                let current = 0;
                const increment = numericTarget / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= numericTarget) {
                        stat.textContent = numericTarget + (isPercentage ? '%' : isPlus ? '+' : '');
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(current) + (isPercentage ? '%' : '');
                    }
                }, 50);
            });

            // تأثير الظهور التدريجي للعناصر
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            document.querySelectorAll('.feature, .step').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'all 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
