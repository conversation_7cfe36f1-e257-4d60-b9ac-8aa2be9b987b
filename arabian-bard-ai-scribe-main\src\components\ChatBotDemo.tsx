import { useState, useRef, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MessageCircle, Send, User, Bot, Copy, Download, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
}

interface ChatBotDemoProps {
  activeModel: string;
}

export const ChatBotDemo = ({ activeModel }: ChatBotDemoProps) => {
  const { toast } = useToast();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: 'مرحباً بك! أنا النسخة التجريبية للمساعد الذكي. سأعطيك ردود تجريبية لتتعلم كيف يعمل النظام. اكتب أي سؤال وسأجيبك!',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [projectType, setProjectType] = useState("رواية");
  const [writingStyle, setWritingStyle] = useState("نجيب محفوظ");
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const projectTypes = ["رواية", "قصة قصيرة", "شعر", "مقال أدبي"];
  const writingStyles = ["نجيب محفوظ", "أحمد مراد", "غسان كنفاني", "أحلام مستغانمي", "طه حسين"];

  // ردود تجريبية حسب نوع المشروع
  const demoResponses = {
    "رواية": [
      "ممتاز! لكتابة رواية بأسلوب نجيب محفوظ، ابدأ بوصف المكان والزمان بتفصيل دقيق. مثلاً: 'كانت القاهرة في ذلك الصباح تبدو كأنها تستيقظ من حلم طويل...'",
      "فكرة رائعة! في الرواية، الشخصيات هي المحرك الأساسي. اخلق شخصية رئيسية لها دوافع واضحة وصراع داخلي يجعل القارئ يتعاطف معها.",
      "بالنسبة للحبكة، فكر في صراع أساسي يواجه بطلك. قد يكون صراعاً اجتماعياً، عاطفياً، أو أخلاقياً. هذا الصراع سيقود أحداث روايتك."
    ],
    "شعر": [
      "الشعر العربي له موسيقى خاصة. اختر بحراً شعرياً مناسباً لموضوعك. للحب والعاطفة، جرب بحر الرمل. للفخر والحماسة، جرب الطويل.",
      "الصورة الشعرية هي روح القصيدة. استخدم التشبيه والاستعارة لتجعل المعنى أكثر جمالاً وتأثيراً في نفس القارئ.",
      "لا تنس الوحدة الموضوعية في القصيدة. كل بيت يجب أن يخدم الفكرة الرئيسية ويضيف إليها شيئاً جديداً."
    ],
    "قصة قصيرة": [
      "القصة القصيرة تركز على لحظة واحدة مؤثرة. ابدأ بنهاية مفاجئة في ذهنك، ثم ابن الأحداث للوصول إليها.",
      "في القصة القصيرة، كل كلمة لها وزن. احذف أي تفصيل لا يخدم الهدف الرئيسي للقصة.",
      "اجعل البداية جذابة والنهاية مؤثرة. القارئ يجب أن يشعر بالتغيير أو الإدراك في نهاية القصة."
    ],
    "مقال أدبي": [
      "المقال الأدبي يحتاج إلى فكرة واضحة وأدلة قوية. ابدأ بمقدمة تطرح السؤال أو المشكلة التي ستناقشها.",
      "استخدم الشواهد من التراث العربي والأدب الكلاسيكي لتقوية حجتك. الأمثلة الملموسة تجعل المقال أكثر إقناعاً.",
      "اختتم بخلاصة تربط كل الأفكار وتترك أثراً في نفس القارئ. الخاتمة الجيدة تجعل المقال لا يُنسى."
    ]
  };

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const getRandomResponse = () => {
    const responses = demoResponses[projectType] || demoResponses["رواية"];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsTyping(true);

    try {
      // استدعاء Groq API مباشرة
      const groqApiKey = import.meta.env.VITE_GROQ_API_KEY;

      if (!groqApiKey) {
        throw new Error('Groq API key not found. Please add VITE_GROQ_API_KEY to .env.local');
      }

      const systemPrompt = `أنت مساعد ذكي متخصص في كتابة الأدب العربي.
نوع المشروع: ${projectType}
أسلوب الكتابة: ${writingStyle}

مهمتك مساعدة المستخدم في الكتابة الإبداعية باللغة العربية الفصحى.
كن ودوداً ومشجعاً وقدم نصائح عملية ومحددة.`;

      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${groqApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'llama-3.3-70b-versatile',
          messages: [
            { role: "system", content: systemPrompt },
            ...messages.slice(-5).map(msg => ({
              role: msg.type === 'user' ? 'user' : 'assistant',
              content: msg.content
            })),
            { role: "user", content: inputMessage }
          ],
          max_tokens: 1000,
          temperature: 0.8,
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Groq API error: ${response.status} - ${errorData}`);
      }

      const data = await response.json();
      const aiResponse = data.choices[0].message.content;

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: aiResponse,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);

      toast({
        title: "رد من الذكاء الاصطناعي",
        description: "تم الحصول على رد من نموذج Llama 3.1",
      });

    } catch (error) {
      console.error('Error calling Groq API:', error);

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: `عذراً، حدث خطأ: ${error.message}. تأكد من إضافة مفتاح Groq API في ملف .env.local`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "خطأ في الاتصال",
        description: "تأكد من إضافة مفتاح Groq API",
        variant: "destructive",
      });
    } finally {
      setIsTyping(false);
    }
  };

  const exportChat = () => {
    const chatContent = messages.map(msg =>
      `${msg.type === 'user' ? 'أنت' : 'المساعد'}: ${msg.content}`
    ).join('\n\n');

    const blob = new Blob([chatContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `حوار-تجريبي-${new Date().toLocaleDateString('ar')}.txt`;
    a.click();
  };

  const copyLastResponse = () => {
    const lastBotMessage = messages.filter(msg => msg.type === 'bot').pop();
    if (lastBotMessage) {
      navigator.clipboard.writeText(lastBotMessage.content);
      toast({
        title: "تم النسخ",
        description: "تم نسخ آخر رد إلى الحافظة",
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">بوت الدردشة الأدبي - مدعوم بـ Llama 3.1</h2>
        <p className="text-slate-600">مساعد ذكي حقيقي للكتابة الأدبية العربية باستخدام نموذج Llama 3.1</p>
        <Badge variant="default" className="mt-2">مدعوم بالذكاء الاصطناعي - AI Powered</Badge>
      </div>

      <div className="grid md:grid-cols-4 gap-6">
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5 text-blue-600" />
              محادثة تجريبية
            </CardTitle>
            <div className="flex gap-2">
              <Badge variant="outline">{activeModel} (تجريبي)</Badge>
              <Badge variant="secondary">{projectType}</Badge>
              <Badge variant="outline">{writingStyle}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96 mb-4 p-4 border rounded-lg bg-slate-50" ref={scrollAreaRef}>
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex gap-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        message.type === 'user' ? 'bg-emerald-100' : 'bg-blue-100'
                      }`}>
                        {message.type === 'user' ? (
                          <User className="w-4 h-4 text-emerald-600" />
                        ) : (
                          <Bot className="w-4 h-4 text-blue-600" />
                        )}
                      </div>
                      <div className={`p-3 rounded-lg ${
                        message.type === 'user'
                          ? 'bg-emerald-600 text-white'
                          : 'bg-white border border-slate-200'
                      }`}>
                        <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
                        <span className="text-xs opacity-70 mt-1 block">
                          {message.timestamp.toLocaleTimeString('ar')}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}

                {isTyping && (
                  <div className="flex gap-3 justify-start">
                    <div className="flex gap-2">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center bg-blue-100">
                        <Bot className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="p-3 rounded-lg bg-white border border-slate-200">
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            <div className="flex gap-2">
              <Input
                placeholder="اكتب رسالتك هنا..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && !isTyping && handleSendMessage()}
                className="flex-1"
                disabled={isTyping}
              />
              <Button onClick={handleSendMessage} disabled={!inputMessage.trim() || isTyping}>
                {isTyping ? <Loader2 className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">إعدادات المحادثة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">نوع المشروع</label>
              <Select value={projectType} onValueChange={setProjectType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {projectTypes.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">أسلوب الكتابة</label>
              <Select value={writingStyle} onValueChange={setWritingStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {writingStyles.map((style) => (
                    <SelectItem key={style} value={style}>{style}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="pt-4 space-y-2">
              <Button variant="outline" size="sm" className="w-full" onClick={exportChat}>
                <Download className="w-4 h-4 mr-2" />
                تصدير المحادثة
              </Button>
              <Button variant="ghost" size="sm" className="w-full" onClick={copyLastResponse}>
                <Copy className="w-4 h-4 mr-2" />
                نسخ آخر رد
              </Button>
            </div>

            <div className="pt-4 border-t">
              <h4 className="font-medium text-sm mb-2">جرب هذه الأسئلة:</h4>
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("كيف أبدأ كتابة رواية؟")}
                >
                  كيف أبدأ كتابة رواية؟
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("ساعدني في كتابة قصيدة")}
                >
                  ساعدني في كتابة قصيدة
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("كيف أطور شخصيات قصتي؟")}
                >
                  كيف أطور شخصيات قصتي؟
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
