import React, { useState } from 'react';
import { OpenSourceModelSelector } from '@/components/OpenSourceModelSelector';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Star, Zap, Globe, Heart, BookOpen } from "lucide-react";
import { Link } from "react-router-dom";

export default function OpenSourceModels() {
  const [selectedModel, setSelectedModel] = useState('mistral-saba-24b');

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Globe className="w-8 h-8 text-emerald-600" />
            <h1 className="text-4xl font-bold text-slate-800">النماذج مفتوحة المصدر</h1>
            <Heart className="w-8 h-8 text-red-500" />
          </div>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            اكتشف أفضل النماذج المجانية والمتخصصة في اللغة العربية للكتابة الأدبية والإبداعية
          </p>
        </div>

        {/* مزايا النماذج مفتوحة المصدر */}
        <div className="grid md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Zap className="w-8 h-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-semibold text-slate-800">مجانية تماماً</h3>
              <p className="text-sm text-slate-600">لا تحتاج اشتراكات أو رسوم</p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <Globe className="w-8 h-8 text-blue-500 mx-auto mb-2" />
              <h3 className="font-semibold text-slate-800">متخصصة في العربية</h3>
              <p className="text-sm text-slate-600">مدربة خصيصاً للثقافة العربية</p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <BookOpen className="w-8 h-8 text-purple-500 mx-auto mb-2" />
              <h3 className="font-semibold text-slate-800">للكتابة الأدبية</h3>
              <p className="text-sm text-slate-600">محسنة للشعر والنثر والروايات</p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <Star className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
              <h3 className="font-semibold text-slate-800">جودة عالية</h3>
              <p className="text-sm text-slate-600">نتائج احترافية ومبدعة</p>
            </CardContent>
          </Card>
        </div>

        {/* مكون اختيار النماذج */}
        <OpenSourceModelSelector 
          activeModel={selectedModel} 
          setActiveModel={setSelectedModel} 
        />

        {/* النماذج الموصى بها خصيصاً */}
        <Card className="mt-8 bg-gradient-to-r from-emerald-50 to-blue-50 border-emerald-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-emerald-800">
              <Star className="w-6 h-6" />
              توصياتنا الخاصة للكتابة العربية
            </CardTitle>
            <CardDescription>
              بناءً على اختباراتنا المكثفة، هذه أفضل النماذج للكتابة الأدبية العربية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="p-4 bg-white rounded-lg border border-emerald-200">
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="bg-emerald-500">الأفضل للشعر</Badge>
                </div>
                <h4 className="font-semibold text-emerald-800">Mistral Saba 24B</h4>
                <p className="text-sm text-emerald-700">
                  متخصص في الشعر العربي والنثر الكلاسيكي، يفهم البحور والقوافي
                </p>
              </div>
              
              <div className="p-4 bg-white rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="bg-blue-500">الأفضل للروايات</Badge>
                </div>
                <h4 className="font-semibold text-blue-800">Llama 3.3 70B</h4>
                <p className="text-sm text-blue-700">
                  ممتاز للنصوص الطويلة والحبكات المعقدة، فهم عميق للسياق
                </p>
              </div>
              
              <div className="p-4 bg-white rounded-lg border border-purple-200">
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="bg-purple-500">الأفضل للتراث</Badge>
                </div>
                <h4 className="font-semibold text-purple-800">JAIS 30B</h4>
                <p className="text-sm text-purple-700">
                  مصمم للثقافة العربية، ممتاز للنصوص التراثية والكلاسيكية
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* دليل الاستخدام السريع */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="w-6 h-6 text-slate-700" />
              كيفية البدء
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-slate-800 mb-3">للمبتدئين:</h4>
                <ol className="space-y-2 text-sm text-slate-600">
                  <li className="flex items-center gap-2">
                    <span className="w-6 h-6 bg-emerald-100 text-emerald-800 rounded-full flex items-center justify-center text-xs font-semibold">1</span>
                    ابدأ بـ Mistral Saba 24B (مجاني وسريع)
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-6 h-6 bg-emerald-100 text-emerald-800 rounded-full flex items-center justify-center text-xs font-semibold">2</span>
                    جرب كتابة نص قصير (100-200 كلمة)
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-6 h-6 bg-emerald-100 text-emerald-800 rounded-full flex items-center justify-center text-xs font-semibold">3</span>
                    استخدم النصائح التفاعلية المعروضة
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-6 h-6 bg-emerald-100 text-emerald-800 rounded-full flex items-center justify-center text-xs font-semibold">4</span>
                    قارن النتائج مع نماذج أخرى
                  </li>
                </ol>
              </div>
              
              <div>
                <h4 className="font-semibold text-slate-800 mb-3">للمتقدمين:</h4>
                <ol className="space-y-2 text-sm text-slate-600">
                  <li className="flex items-center gap-2">
                    <span className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-semibold">1</span>
                    استخدم Llama 3.3 70B للمشاريع الكبيرة
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-semibold">2</span>
                    جرب نماذج متعددة لنفس النص
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-semibold">3</span>
                    طور توجيهات مخصصة لأسلوبك
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-semibold">4</span>
                    ادمج النتائج لإنتاج محتوى فريد
                  </li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* أزرار التنقل */}
        <div className="flex justify-center gap-4 mt-8">
          <Link to="/writing">
            <Button className="bg-emerald-600 hover:bg-emerald-700 text-white">
              <BookOpen className="w-4 h-4 mr-2" />
              ابدأ الكتابة الآن
              <ArrowRight className="w-4 h-4 mr-2" />
            </Button>
          </Link>
          
          <Link to="/chat">
            <Button variant="outline" className="border-emerald-600 text-emerald-600 hover:bg-emerald-50">
              جرب الشات بوت
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
