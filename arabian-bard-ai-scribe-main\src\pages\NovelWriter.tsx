
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BookOpen, Save, Download, FileText, BarChart3, Settings, Plus, Edit3 } from "lucide-react";

const NovelWriter = () => {
  const [novelTitle, setNovelTitle] = useState("");
  const [currentChapter, setCurrentChapter] = useState(1);
  const [chapters, setChapters] = useState([
    { id: 1, title: "الفصل الأول", content: "", wordCount: 0 }
  ]);
  const [targetWords, setTargetWords] = useState(100000);
  const [writingStyle, setWritingStyle] = useState("نجيب محفوظ");
  const [currentContent, setCurrentContent] = useState("");

  const writingStyles = ["نجيب محفوظ", "أحمد مراد", "غسان كنفاني", "أحلام مستغانمي", "طه حسين"];

  const totalWords = chapters.reduce((sum, chapter) => sum + chapter.wordCount, 0);
  const progress = (totalWords / targetWords) * 100;

  const addNewChapter = () => {
    const newChapter = {
      id: chapters.length + 1,
      title: `الفصل ${chapters.length + 1}`,
      content: "",
      wordCount: 0
    };
    setChapters([...chapters, newChapter]);
    setCurrentChapter(newChapter.id);
  };

  const updateChapterContent = (content: string) => {
    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
    setCurrentContent(content);
    
    setChapters(prev => prev.map(chapter => 
      chapter.id === currentChapter 
        ? { ...chapter, content, wordCount }
        : chapter
    ));
  };

  const generateContent = async () => {
    // محاكاة توليد المحتوى
    const sampleContent = `في ذلك الصباح الربيعي الجميل، كانت القاهرة تبدو وكأنها ترقص على أنغام الحياة. الشوارع تعج بالحركة، والناس يسيرون بخطوات متسارعة نحو أعمالهم وأحلامهم. أحمد، الشاب الثلاثيني، وقف عند نافذة شقته الصغيرة في حي الزمالك، يتأمل هذا المشهد اليومي الذي لا يمل من مراقبته.

كان قد أمضى ليلة أخرى بلا نوم، يفكر في مستقبله وفي الطريق الذي يريد أن يسلكه. العمل في الشركة لم يعد يشبع طموحاته، والحلم القديم بأن يصبح كاتباً ما زال يراوده كل ليلة. في جيبه رسالة من دار نشر تخبره بقبول روايته الأولى، لكنه لم يجد الشجاعة بعد لفتحها.

"اليوم سيكون مختلفاً"، همس لنفسه وهو يرتدي ملابسه. قرر أن يأخذ إجازة من العمل ويقضي اليوم في كتابة الفصل الجديد من روايته الثانية. كان يعلم أن الطريق طويل وشاق، لكن شيئاً في قلبه يخبره أن الوقت قد حان للمخاطرة والإيمان بأحلامه.`;

    updateChapterContent(currentContent + sampleContent);
  };

  const currentChapterData = chapters.find(ch => ch.id === currentChapter);

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-emerald-50 p-6" dir="rtl">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-slate-800 mb-2">ورشة كتابة الروايات الطويلة</h1>
          <p className="text-slate-600">اكتب روايات تصل إلى 100,000 كلمة بمساعدة الذكاء الاصطناعي</p>
        </div>

        <div className="grid lg:grid-cols-4 gap-6">
          {/* الشريط الجانبي */}
          <Card className="lg:row-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5 text-purple-600" />
                تنظيم الرواية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">عنوان الرواية</label>
                <Input
                  value={novelTitle}
                  onChange={(e) => setNovelTitle(e.target.value)}
                  placeholder="ادخل عنوان روايتك"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">الهدف (كلمة)</label>
                <Input
                  type="number"
                  value={targetWords}
                  onChange={(e) => setTargetWords(Number(e.target.value))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">أسلوب الكتابة</label>
                <Select value={writingStyle} onValueChange={setWritingStyle}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {writingStyles.map((style) => (
                      <SelectItem key={style} value={style}>{style}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">التقدم الإجمالي</span>
                  <span className="text-sm text-slate-600">{totalWords.toLocaleString()} / {targetWords.toLocaleString()}</span>
                </div>
                <Progress value={Math.min(progress, 100)} className="w-full" />
                <span className="text-xs text-slate-500">{progress.toFixed(1)}% مكتمل</span>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">الفصول</h4>
                  <Button size="sm" onClick={addNewChapter}>
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                <div className="space-y-1 max-h-40 overflow-y-auto">
                  {chapters.map((chapter) => (
                    <Button
                      key={chapter.id}
                      variant={currentChapter === chapter.id ? "default" : "ghost"}
                      size="sm"
                      className="w-full justify-start text-xs"
                      onClick={() => {
                        setCurrentChapter(chapter.id);
                        setCurrentContent(chapter.content);
                      }}
                    >
                      <div className="flex-1 text-right">
                        <div>{chapter.title}</div>
                        <div className="text-xs opacity-70">{chapter.wordCount} كلمة</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* منطقة الكتابة الرئيسية */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Edit3 className="w-5 h-5 text-emerald-600" />
                {currentChapterData?.title}
              </CardTitle>
              <div className="flex gap-2">
                <Badge variant="outline">{currentChapterData?.wordCount} كلمة</Badge>
                <Badge variant="secondary">{writingStyle}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="write" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="write">الكتابة</TabsTrigger>
                  <TabsTrigger value="ai-assist">المساعد الذكي</TabsTrigger>
                  <TabsTrigger value="settings">الإعدادات</TabsTrigger>
                </TabsList>

                <TabsContent value="write" className="space-y-4">
                  <Textarea
                    value={currentContent}
                    onChange={(e) => updateChapterContent(e.target.value)}
                    placeholder="ابدأ كتابة فصلك هنا..."
                    className="min-h-[500px] text-lg leading-relaxed"
                  />
                  
                  <div className="flex gap-2">
                    <Button onClick={generateContent} className="bg-emerald-600 hover:bg-emerald-700">
                      توليد محتوى ذكي
                    </Button>
                    <Button variant="outline">
                      <Save className="w-4 h-4 mr-2" />
                      حفظ
                    </Button>
                    <Button variant="outline">
                      <Download className="w-4 h-4 mr-2" />
                      تصدير
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="ai-assist" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <h4 className="font-medium mb-2">تطوير الشخصيات</h4>
                        <p className="text-sm text-slate-600 mb-3">احصل على أفكار لتطوير شخصيات مثيرة ومعقدة</p>
                        <Button size="sm" variant="outline" className="w-full">استخدم المساعد</Button>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <h4 className="font-medium mb-2">بناء الحبكة</h4>
                        <p className="text-sm text-slate-600 mb-3">احصل على اقتراحات لتطوير الأحداث والصراعات</p>
                        <Button size="sm" variant="outline" className="w-full">استخدم المساعد</Button>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <h4 className="font-medium mb-2">وصف المشاهد</h4>
                        <p className="text-sm text-slate-600 mb-3">احصل على مساعدة في وصف الأماكن والمشاهد</p>
                        <Button size="sm" variant="outline" className="w-full">استخدم المساعد</Button>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <h4 className="font-medium mb-2">الحوارات</h4>
                        <p className="text-sm text-slate-600 mb-3">تحسين الحوارات وجعلها أكثر طبيعية</p>
                        <Button size="sm" variant="outline" className="w-full">استخدم المساعد</Button>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="settings" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">طول الفقرة المولدة</label>
                      <Select defaultValue="medium">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="short">قصيرة (100-200 كلمة)</SelectItem>
                          <SelectItem value="medium">متوسطة (300-500 كلمة)</SelectItem>
                          <SelectItem value="long">طويلة (600-1000 كلمة)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">مستوى الإبداع</label>
                      <Select defaultValue="balanced">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="conservative">محافظ</SelectItem>
                          <SelectItem value="balanced">متوازن</SelectItem>
                          <SelectItem value="creative">إبداعي</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* إحصائيات سريعة */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-orange-600" />
                إحصائيات سريعة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-5 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-700">{totalWords.toLocaleString()}</div>
                  <div className="text-sm text-blue-600">إجمالي الكلمات</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-700">{chapters.length}</div>
                  <div className="text-sm text-green-600">عدد الفصول</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-700">{Math.ceil(totalWords / 250)}</div>
                  <div className="text-sm text-purple-600">عدد الصفحات التقريبي</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-700">{Math.ceil((targetWords - totalWords) / 1000)}</div>
                  <div className="text-sm text-orange-600">كلمات متبقية (آلاف)</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-700">{Math.ceil(totalWords / 500)}</div>
                  <div className="text-sm text-red-600">ساعات قراءة تقريبية</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default NovelWriter;
