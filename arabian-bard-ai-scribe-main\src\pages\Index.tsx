
import { useState } from "react";
import { Header } from "@/components/Header";
import { ModelSelection } from "@/components/ModelSelection";
import { DataPreparation } from "@/components/DataPreparation";
import { TrainingPanel } from "@/components/TrainingPanel";
import { WritingInterface } from "@/components/WritingInterface";
import { ModelMetrics } from "@/components/ModelMetrics";
import { ChatBot } from "@/components/ChatBot";
import { ChatBotDemo } from "@/components/ChatBotDemo";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BookOpen, MessageCircle, FileText, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

const Index = () => {
  const [activeModel, setActiveModel] = useState("gpt-4o-mini");
  const [trainingProgress, setTrainingProgress] = useState(0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-emerald-50" dir="rtl">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-slate-800 mb-4 leading-tight">
            نموذج الذكاء الاصطناعي الأدبي العربي
          </h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed mb-8">
            منصة متطورة لتطوير وتدريب نماذج الذكاء الاصطناعي المتخصصة في الأدب العربي
            باستخدام أحدث تقنيات التعلم الآلي والنماذج مفتوحة المصدر
          </p>

          {/* روابط سريعة للمشاريع الكبيرة */}
          <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto mb-12">
            <Link to="/novel-writer">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="w-6 h-6 text-purple-600" />
                    ورشة الروايات الطويلة
                  </CardTitle>
                  <CardDescription>
                    اكتب روايات تصل إلى 100,000 كلمة بمساعدة الذكاء الاصطناعي
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    ابدأ كتابة روايتك
                    <ArrowRight className="w-4 h-4 mr-2" />
                  </Button>
                </CardContent>
              </Card>
            </Link>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="w-6 h-6 text-blue-600" />
                  بوت الدردشة الأدبي
                </CardTitle>
                <CardDescription>
                  احترف كتابة الأدب من خلال حوار تفاعلي مع الذكاء الاصطناعي
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-slate-600 mb-4">
                  متوفر في تبويب "الدردشة الأدبية" أدناه
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        <Tabs defaultValue="model-selection" className="w-full">
          <TabsList className="grid w-full grid-cols-6 mb-8 bg-white/80 backdrop-blur-sm">
            <TabsTrigger value="model-selection" className="text-sm">اختيار النموذج</TabsTrigger>
            <TabsTrigger value="data-prep" className="text-sm">إعداد البيانات</TabsTrigger>
            <TabsTrigger value="training" className="text-sm">التدريب</TabsTrigger>
            <TabsTrigger value="writing" className="text-sm">الكتابة الإبداعية</TabsTrigger>
            <TabsTrigger value="chat" className="text-sm">الدردشة الأدبية</TabsTrigger>
            <TabsTrigger value="metrics" className="text-sm">التقييم والمقاييس</TabsTrigger>
          </TabsList>

          <TabsContent value="model-selection">
            <ModelSelection
              activeModel={activeModel}
              setActiveModel={setActiveModel}
            />
          </TabsContent>

          <TabsContent value="data-prep">
            <DataPreparation />
          </TabsContent>

          <TabsContent value="training">
            <TrainingPanel
              progress={trainingProgress}
              setProgress={setTrainingProgress}
            />
          </TabsContent>

          <TabsContent value="writing">
            <WritingInterface activeModel={activeModel} />
          </TabsContent>

          <TabsContent value="chat">
            <ChatBot activeModel={activeModel} />
          </TabsContent>

          <TabsContent value="metrics">
            <ModelMetrics />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default Index;
