import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BookOpen, Target, Award, RefreshCw, Eye, Lightbulb } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { getRandomTrainingText, getTrainingTextsByAuthor } from "@/data/trainingTexts";
import { buildRAGEnhancedPrompt, analyzeGeneratedText, getSuggestions } from "@/utils/ragSystem";

interface InteractiveTrainingProps {
  activeModel: string;
}

export const InteractiveTraining = ({ activeModel }: InteractiveTrainingProps) => {
  const [selectedAuthor, setSelectedAuthor] = useState("نجيب محفوظ");
  const [currentExercise, setCurrentExercise] = useState(null);
  const [userResponse, setUserResponse] = useState("");
  const [analysis, setAnalysis] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [trainingScore, setTrainingScore] = useState(0);
  const [exerciseCount, setExerciseCount] = useState(0);
  const [showHints, setShowHints] = useState(false);
  const { toast } = useToast();

  const authors = ["نجيب محفوظ", "أحمد مراد"];
  const contentTypes = [
    { value: "character", label: "وصف الشخصيات" },
    { value: "place", label: "وصف الأماكن" },
    { value: "dialogue", label: "الحوار" },
    { value: "narrative", label: "السرد" },
    { value: "opening", label: "البدايات" }
  ];

  // تحميل تمرين جديد
  const loadNewExercise = () => {
    const exercise = getRandomTrainingText(selectedAuthor);
    setCurrentExercise(exercise);
    setUserResponse("");
    setAnalysis(null);
    setShowHints(false);
  };

  // تحليل إجابة المستخدم
  const analyzeResponse = async () => {
    if (!userResponse.trim() || !currentExercise) return;

    setIsAnalyzing(true);
    try {
      const result = analyzeGeneratedText(
        userResponse,
        selectedAuthor,
        currentExercise.type
      );

      setAnalysis(result);
      setTrainingScore(prev => prev + result.score);
      setExerciseCount(prev => prev + 1);

      // إظهار النتيجة
      if (result.score >= 8) {
        toast({
          title: "ممتاز! 🎉",
          description: `حصلت على ${result.score}/10 - أسلوب رائع!`,
        });
      } else if (result.score >= 6) {
        toast({
          title: "جيد جداً! 👍",
          description: `حصلت على ${result.score}/10 - يمكن تحسينه`,
        });
      } else {
        toast({
          title: "يحتاج تحسين 📚",
          description: `حصلت على ${result.score}/10 - راجع النصائح`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "خطأ في التحليل",
        description: "حدث خطأ أثناء تحليل النص",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // تحميل تمرين عند تغيير الكاتب
  useEffect(() => {
    loadNewExercise();
  }, [selectedAuthor]);

  // حساب متوسط النقاط
  const averageScore = exerciseCount > 0 ? (trainingScore / exerciseCount).toFixed(1) : 0;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">التدريب التفاعلي</h2>
        <p className="text-slate-600">
          تدرب على الكتابة بأساليب الكتاب العرب العظماء مع التقييم الفوري
        </p>
      </div>

      {/* إحصائيات التدريب */}
      <div className="grid md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-slate-600">التمارين المكتملة</p>
                <p className="text-2xl font-bold">{exerciseCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Award className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-slate-600">متوسط النقاط</p>
                <p className="text-2xl font-bold">{averageScore}/10</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm text-slate-600">الكاتب الحالي</p>
                <p className="text-lg font-semibold">{selectedAuthor}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="exercise" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="exercise">التمرين</TabsTrigger>
          <TabsTrigger value="analysis">التحليل</TabsTrigger>
          <TabsTrigger value="knowledge">قاعدة المعرفة</TabsTrigger>
        </TabsList>

        <TabsContent value="exercise" className="space-y-4">
          <div className="grid md:grid-cols-2 gap-6">
            {/* التمرين الحالي */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-600" />
                  التمرين الحالي
                </CardTitle>
                <CardDescription>
                  <div className="flex gap-2 mb-2">
                    <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {authors.map((author) => (
                          <SelectItem key={author} value={author}>{author}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="sm" onClick={loadNewExercise}>
                      <RefreshCw className="w-4 h-4 mr-1" />
                      تمرين جديد
                    </Button>
                  </div>
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {currentExercise && (
                  <>
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{currentExercise.type}</Badge>
                        <Badge variant="secondary">{currentExercise.difficulty}</Badge>
                      </div>
                      <p className="font-medium text-blue-900">
                        {currentExercise.prompt}
                      </p>
                    </div>

                    <Textarea
                      placeholder="اكتب إجابتك هنا بأسلوب الكاتب المحدد..."
                      value={userResponse}
                      onChange={(e) => setUserResponse(e.target.value)}
                      className="min-h-[200px]"
                    />

                    <div className="flex gap-2">
                      <Button 
                        onClick={analyzeResponse}
                        disabled={!userResponse.trim() || isAnalyzing}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        {isAnalyzing ? "جاري التحليل..." : "تحليل الإجابة"}
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setShowHints(!showHints)}
                      >
                        <Lightbulb className="w-4 h-4 mr-1" />
                        {showHints ? "إخفاء النصائح" : "إظهار النصائح"}
                      </Button>
                    </div>

                    {showHints && (
                      <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                        <h4 className="font-medium text-amber-900 mb-2">نصائح للكتابة:</h4>
                        <ul className="text-sm text-amber-800 space-y-1">
                          {getSuggestions(selectedAuthor, currentExercise.type).map((suggestion, index) => (
                            <li key={index}>• {suggestion}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>

            {/* النموذج المثالي */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5 text-green-600" />
                  النموذج المثالي
                </CardTitle>
                <CardDescription>
                  مثال من أعمال {selectedAuthor}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {currentExercise && (
                  <div className="space-y-4">
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <p className="text-green-900 leading-relaxed">
                        {currentExercise.response}
                      </p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">التقنيات المستخدمة:</h4>
                      <div className="flex flex-wrap gap-1">
                        {currentExercise.techniques.map((technique, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {technique}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          {analysis ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5 text-purple-600" />
                  تحليل الأداء
                </CardTitle>
                <CardDescription>
                  النقاط: {analysis.score}/10
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">النقاط</span>
                    <span className="text-sm text-slate-600">{analysis.score}/10</span>
                  </div>
                  <Progress value={analysis.score * 10} className="h-2" />
                </div>

                {analysis.matchedTechniques.length > 0 && (
                  <div>
                    <h4 className="font-medium text-green-700 mb-2">✅ التقنيات المطبقة:</h4>
                    <div className="flex flex-wrap gap-1">
                      {analysis.matchedTechniques.map((technique, index) => (
                        <Badge key={index} variant="default" className="bg-green-100 text-green-800">
                          {technique}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {analysis.matchedVocabulary.length > 0 && (
                  <div>
                    <h4 className="font-medium text-blue-700 mb-2">📚 المفردات المستخدمة:</h4>
                    <div className="flex flex-wrap gap-1">
                      {analysis.matchedVocabulary.map((word, index) => (
                        <Badge key={index} variant="outline" className="text-blue-700">
                          {word}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {analysis.feedback.length > 0 && (
                  <div>
                    <h4 className="font-medium text-orange-700 mb-2">📝 ملاحظات:</h4>
                    <ul className="text-sm text-orange-800 space-y-1">
                      {analysis.feedback.map((note, index) => (
                        <li key={index}>• {note}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {analysis.suggestions.length > 0 && (
                  <div>
                    <h4 className="font-medium text-red-700 mb-2">💡 اقتراحات للتحسين:</h4>
                    <ul className="text-sm text-red-800 space-y-1">
                      {analysis.suggestions.map((suggestion, index) => (
                        <li key={index}>• {suggestion}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-slate-600">قم بحل تمرين أولاً لرؤية التحليل</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="knowledge" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>قاعدة المعرفة</CardTitle>
              <CardDescription>
                استكشف أساليب وتقنيات الكتاب العرب
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {authors.map((author) => (
                  <div key={author} className="p-4 border rounded-lg">
                    <h3 className="font-semibold mb-2">{author}</h3>
                    <p className="text-sm text-slate-600 mb-2">
                      {author === "نجيب محفوظ" 
                        ? "الواقعية الاجتماعية والتحليل النفسي العميق"
                        : "الإثارة والتشويق المعاصر"
                      }
                    </p>
                    <div className="text-xs text-slate-500">
                      {getTrainingTextsByAuthor(author).length} نص تدريبي متاح
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
