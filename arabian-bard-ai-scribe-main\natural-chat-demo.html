<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 الدردشة الأدبية الطبيعية - مثل ChatGPT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            height: 90vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .settings {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .model-selector, .author-selector {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 0.9em;
        }
        
        .model-selector option, .author-selector option {
            background: #667eea;
            color: white;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .message {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            flex-shrink: 0;
        }
        
        .avatar.bot {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .avatar.user {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .message-content {
            max-width: 70%;
            background: white;
            border-radius: 18px;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        
        .message-text {
            line-height: 1.6;
            white-space: pre-wrap;
        }
        
        .message-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            font-size: 0.75em;
            color: #6c757d;
        }
        
        .message.user .message-meta {
            color: rgba(255,255,255,0.8);
        }
        
        .model-badge {
            background: #e9ecef;
            color: #495057;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7em;
        }
        
        .message.user .model-badge {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .typing-indicator {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .typing-content {
            background: white;
            border-radius: 18px;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #dee2e6;
        }
        
        .input-container {
            display: flex;
            gap: 12px;
            align-items: end;
        }
        
        .input-field {
            flex: 1;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 1em;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            font-family: inherit;
            transition: border-color 0.3s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .send-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }
        
        .send-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .input-meta {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-top: 8px;
            font-size: 0.8em;
            color: #6c757d;
        }
        
        .clear-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }
        
        .clear-button:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        
        .welcome-message h2 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .example-prompts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        
        .example-prompt {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.9em;
        }
        
        .example-prompt:hover {
            border-color: #667eea;
            background: #f8f9ff;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                💬 الدردشة الأدبية الطبيعية
            </h1>
            <div class="settings">
                <select class="model-selector" id="modelSelect">
                    <option value="llama-3.3-70b">Llama 3.3 70B (مجاني)</option>
                    <option value="mistral-saba-24b">Mistral Saba 24B (عربي)</option>
                    <option value="llama-3.1-8b">Llama 3.1 8B (سريع)</option>
                    <option value="gpt-4o-mini">GPT-4o Mini</option>
                    <option value="gpt-4o">GPT-4o (متقدم)</option>
                    <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                </select>
                
                <select class="author-selector" id="authorSelect">
                    <option value="نجيب محفوظ">📚 نجيب محفوظ</option>
                    <option value="أحمد مراد">🔍 أحمد مراد</option>
                    <option value="عام">✍️ أسلوب عام</option>
                </select>
                
                <button class="clear-button" onclick="clearChat()">مسح المحادثة</button>
            </div>
        </div>
        
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="welcome-message">
                    <h2>مرحباً بك في الدردشة الأدبية الطبيعية! 🎨</h2>
                    <p>تحدث معي بطريقة طبيعية وسأساعدك في الكتابة بأساليب الكتاب العرب العظماء</p>
                    
                    <div class="example-prompts">
                        <div class="example-prompt" onclick="useExample('اكتب لي قصة قصيرة عن رجل في حارة شعبية')">
                            📖 قصة عن حارة شعبية
                        </div>
                        <div class="example-prompt" onclick="useExample('ساعدني في كتابة بداية مشوقة لرواية')">
                            🚀 بداية مشوقة لرواية
                        </div>
                        <div class="example-prompt" onclick="useExample('كيف أصف شخصية بأسلوب نجيب محفوظ؟')">
                            👤 وصف الشخصيات
                        </div>
                        <div class="example-prompt" onclick="useExample('أريد كتابة حوار متوتر بأسلوب أحمد مراد')">
                            💬 حوار متوتر
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="input-area">
                <div class="input-container">
                    <textarea 
                        class="input-field" 
                        id="messageInput" 
                        placeholder="اكتب رسالتك هنا... تحدث معي بطريقة طبيعية!"
                        onkeypress="handleKeyPress(event)"
                    ></textarea>
                    <button class="send-button" id="sendButton" onclick="sendMessage()">
                        <span id="sendIcon">➤</span>
                    </button>
                </div>
                <div class="input-meta">
                    <span>اضغط Enter للإرسال • Shift + Enter للسطر الجديد</span>
                    <span id="modelInfo">النموذج: Llama 3.3 70B • الأسلوب: نجيب محفوظ</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let messages = [];
        let isTyping = false;

        // قاعدة بيانات النماذج
        const models = {
            'llama-3.3-70b': { name: 'Llama 3.3 70B', provider: 'groq', free: true },
            'mistral-saba-24b': { name: 'Mistral Saba 24B', provider: 'groq', free: true },
            'llama-3.1-8b': { name: 'Llama 3.1 8B', provider: 'groq', free: true },
            'gpt-4o-mini': { name: 'GPT-4o Mini', provider: 'openai', free: false },
            'gpt-4o': { name: 'GPT-4o', provider: 'openai', free: false },
            'claude-3-sonnet': { name: 'Claude 3 Sonnet', provider: 'anthropic', free: false }
        };

        // قاعدة بيانات الردود المحاكاة
        const responses = {
            "نجيب محفوظ": [
                "في الحقيقة، الكتابة عن الحارة الشعبية تتطلب فهماً عميقاً لطبيعة الناس وحياتهم اليومية. دعني أساعدك في رسم صورة حية لهذا العالم الصغير المكتمل...",
                "كما تعلم، الشخصية في الرواية ليست مجرد اسم وصفات، بل هي كائن حي له تاريخ وأحلام ومخاوف. دعني أوضح لك كيف نبني شخصية مقنعة...",
                "الوصف في الأدب فن دقيق، يجب أن يكون مفصلاً دون إملال، وحيوياً دون مبالغة. إليك الطريقة التي أتبعها..."
            ],
            "أحمد مراد": [
                "التشويق يبدأ من الجملة الأولى! يجب أن تجذب القارئ من اللحظة الأولى وتجعله يريد معرفة المزيد. دعني أريك كيف...",
                "الحوار المتوتر يحتاج إلى إيقاع سريع وكلمات مختارة بعناية. كل كلمة يجب أن تزيد من التوتر. هكذا نفعل ذلك...",
                "بناء الإثارة فن يحتاج إلى صبر وتخطيط. لا يمكن أن تكشف كل أوراقك مرة واحدة. دعني أشرح لك الطريقة..."
            ],
            "عام": [
                "الكتابة الإبداعية تحتاج إلى ممارسة مستمرة وقراءة واسعة. دعني أساعدك في تطوير أسلوبك الخاص...",
                "كل كاتب له صوته المميز، والمهم هو أن تجد صوتك الخاص. إليك بعض النصائح التي قد تساعدك...",
                "الأدب العربي غني بالتقنيات والأساليب المختلفة. دعنا نستكشف بعضها معاً..."
            ]
        };

        // دالة إرسال الرسالة
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isTyping) return;
            
            // إضافة رسالة المستخدم
            addMessage('user', message);
            input.value = '';
            
            // إظهار مؤشر الكتابة
            showTyping();
            
            // محاكاة الرد بعد تأخير
            setTimeout(() => {
                hideTyping();
                const response = generateResponse(message);
                addMessage('assistant', response);
            }, 1500 + Math.random() * 1000);
        }

        // دالة إضافة رسالة
        function addMessage(role, content) {
            const messagesContainer = document.getElementById('messages');
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            
            if (welcomeMessage) {
                welcomeMessage.remove();
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const selectedModel = document.getElementById('modelSelect').value;
            const modelInfo = models[selectedModel];
            
            messageDiv.innerHTML = `
                <div class="avatar ${role}">
                    ${role === 'user' ? '👤' : '🤖'}
                </div>
                <div class="message-content">
                    <div class="message-text">${content}</div>
                    <div class="message-meta">
                        <span>${new Date().toLocaleTimeString('ar')}</span>
                        ${role === 'assistant' ? `<span class="model-badge">${modelInfo.name}</span>` : ''}
                        ${modelInfo.free ? '<span style="color: green;">مجاني</span>' : '<span style="color: orange;">مدفوع</span>'}
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            messages.push({ role, content, timestamp: new Date() });
        }

        // دالة إظهار مؤشر الكتابة
        function showTyping() {
            isTyping = true;
            const messagesContainer = document.getElementById('messages');
            
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.id = 'typingIndicator';
            
            typingDiv.innerHTML = `
                <div class="avatar bot">🤖</div>
                <div class="typing-content">
                    <span>يكتب</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            document.getElementById('sendButton').disabled = true;
            document.getElementById('sendIcon').textContent = '⏳';
        }

        // دالة إخفاء مؤشر الكتابة
        function hideTyping() {
            isTyping = false;
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
            
            document.getElementById('sendButton').disabled = false;
            document.getElementById('sendIcon').textContent = '➤';
        }

        // دالة توليد الرد
        function generateResponse(userMessage) {
            const selectedAuthor = document.getElementById('authorSelect').value;
            const authorResponses = responses[selectedAuthor] || responses['عام'];
            
            // اختيار رد عشوائي وتخصيصه
            let response = authorResponses[Math.floor(Math.random() * authorResponses.length)];
            
            // إضافة تخصيص بناءً على رسالة المستخدم
            if (userMessage.includes('قصة') || userMessage.includes('حكاية')) {
                response += "\n\nكان عبد الرحمن رجلاً في الستين من عمره، نحيل الجسم، أبيض الشعر، تتوهج عيناه الصغيرتان بذكاء حاد يعكس سنوات طويلة من التأمل والمراقبة...";
            } else if (userMessage.includes('حوار') || userMessage.includes('كلام')) {
                response += "\n\n'إيه رأيك نبدأ بحوار بسيط؟' سأل بصوت هادئ. 'أعتقد إن ده هيكون مفيد جداً' أجاب بحماس واضح.";
            } else if (userMessage.includes('شخصية') || userMessage.includes('وصف')) {
                response += "\n\nالوصف الجيد يجمع بين الملامح الجسدية والنفسية، ويربط بين المظهر والشخصية بطريقة طبيعية ومقنعة.";
            }
            
            return response;
        }

        // دالة استخدام الأمثلة
        function useExample(text) {
            document.getElementById('messageInput').value = text;
            sendMessage();
        }

        // دالة مسح المحادثة
        function clearChat() {
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <h2>تم مسح المحادثة 🗑️</h2>
                    <p>يمكنك البدء في محادثة جديدة الآن</p>
                </div>
            `;
            messages = [];
        }

        // دالة التعامل مع الضغط على المفاتيح
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // تحديث معلومات النموذج
        function updateModelInfo() {
            const selectedModel = document.getElementById('modelSelect').value;
            const selectedAuthor = document.getElementById('authorSelect').value;
            const modelInfo = models[selectedModel];
            
            document.getElementById('modelInfo').textContent = 
                `النموذج: ${modelInfo.name} • الأسلوب: ${selectedAuthor}`;
        }

        // ربط الأحداث
        document.getElementById('modelSelect').addEventListener('change', updateModelInfo);
        document.getElementById('authorSelect').addEventListener('change', updateModelInfo);

        // رسالة ترحيب تلقائية
        setTimeout(() => {
            addMessage('assistant', 'مرحباً! أنا مساعدك الأدبي الذكي. يمكنني مساعدتك في الكتابة بأساليب الكتاب العرب العظماء مثل نجيب محفوظ وأحمد مراد. ما الذي تريد أن نكتب عنه اليوم؟');
        }, 1000);
    </script>
</body>
</html>
