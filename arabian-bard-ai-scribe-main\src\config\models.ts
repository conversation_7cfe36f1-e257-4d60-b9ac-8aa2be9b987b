// تكوين النماذج المتقدم مع معلومات مفصلة

export interface ModelConfig {
  id: string;
  name: string;
  provider: 'openai' | 'groq' | 'anthropic';
  model: string;
  maxTokens: number;
  temperature: number;
  description: string;
  strengths: string[];
  bestFor: string[];
  status: 'stable' | 'beta' | 'deprecated';
  cost: 'low' | 'medium' | 'high';
  speed: 'fast' | 'medium' | 'slow';
}

export const modelConfigs: ModelConfig[] = [
  {
    id: 'mistral-saba-24b',
    name: 'Mistral Saba 24B',
    provider: 'groq',
    model: 'mistral-saba-24b',
    maxTokens: 1500,
    temperature: 0.8,
    description: 'نموذج متخصص في العربية والفارسية، مجاني وسريع',
    strengths: [
      'متخصص في اللغة العربية',
      'سريع جداً على Groq',
      'مجاني تماماً',
      'ممتاز للشعر والأدب العربي'
    ],
    bestFor: [
      'الكتابة الأدبية العربية',
      'الشعر العربي',
      'النثر الكلاسيكي',
      'المحتوى الثقافي العربي'
    ],
    status: 'stable',
    cost: 'low',
    speed: 'fast'
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'openai',
    model: 'gpt-4o-mini',
    maxTokens: 1500,
    temperature: 0.8,
    description: 'نموذج OpenAI المحسن للاستخدام العام، سريع ومستقر',
    strengths: [
      'استقرار عالي',
      'جودة ممتازة في اللغة العربية',
      'سرعة استجابة جيدة',
      'تكلفة معقولة'
    ],
    bestFor: [
      'الكتابة الإبداعية',
      'المحادثات التفاعلية',
      'توليد المحتوى المتنوع'
    ],
    status: 'stable',
    cost: 'medium',
    speed: 'fast'
  },
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'openai',
    model: 'gpt-4o',
    maxTokens: 2000,
    temperature: 0.7,
    description: 'أحدث نموذج من OpenAI بقدرات متقدمة',
    strengths: [
      'أعلى جودة في النصوص',
      'فهم عميق للسياق',
      'إبداع متقدم',
      'دقة لغوية عالية'
    ],
    bestFor: [
      'الروايات الطويلة',
      'النصوص المعقدة',
      'التحليل الأدبي'
    ],
    status: 'stable',
    cost: 'high',
    speed: 'medium'
  },
  {
    id: 'llama-3.3-70b',
    name: 'Llama 3.3 70B',
    provider: 'groq',
    model: 'llama-3.3-70b-versatile',
    maxTokens: 2000,
    temperature: 0.8,
    description: 'أحدث نموذج Llama بجودة عالية ودعم ممتاز للعربية',
    strengths: [
      'جودة عالية جداً',
      'فهم عميق للسياق العربي',
      'ممتاز للنصوص الطويلة',
      'مجاني على Groq'
    ],
    bestFor: [
      'الروايات الطويلة',
      'التحليل الأدبي المعقد',
      'الكتابة الإبداعية المتقدمة',
      'الترجمة الأدبية'
    ],
    status: 'stable',
    cost: 'low',
    speed: 'medium'
  },
  {
    id: 'llama-3.1-8b',
    name: 'Llama 3.1 8B Instant',
    provider: 'groq',
    model: 'llama-3.1-8b-instant',
    maxTokens: 1500,
    temperature: 0.8,
    description: 'نموذج Llama السريع والمجاني من Groq',
    strengths: [
      'سرعة فائقة',
      'مجاني الاستخدام',
      'جودة جيدة',
      'دعم اللغة العربية'
    ],
    bestFor: [
      'التجارب السريعة',
      'المحادثات التفاعلية',
      'النماذج الأولية'
    ],
    status: 'stable',
    cost: 'low',
    speed: 'fast'
  },
  {
    id: 'claude-3-sonnet',
    name: 'Claude 3 Sonnet',
    provider: 'anthropic',
    model: 'claude-3-sonnet-20240229',
    maxTokens: 1500,
    temperature: 0.8,
    description: 'نموذج Anthropic المتوازن بين الجودة والسرعة',
    strengths: [
      'تفكير منطقي متقدم',
      'أمان عالي',
      'جودة ممتازة في الكتابة',
      'فهم عميق للسياق'
    ],
    bestFor: [
      'النصوص الأدبية المعقدة',
      'التحليل والنقد',
      'الكتابة الأكاديمية'
    ],
    status: 'stable',
    cost: 'medium',
    speed: 'medium'
  }
];

// دالة للحصول على تكوين النموذج
export function getModelConfig(modelId: string): ModelConfig | undefined {
  return modelConfigs.find(config => config.id === modelId);
}

// دالة للحصول على النماذج حسب المزود
export function getModelsByProvider(provider: string): ModelConfig[] {
  return modelConfigs.filter(config => config.provider === provider);
}

// دالة للحصول على النماذج المستقرة فقط
export function getStableModels(): ModelConfig[] {
  return modelConfigs.filter(config => config.status === 'stable');
}

// دالة للحصول على أفضل نموذج لنوع معين من المحتوى
export function getBestModelFor(contentType: string): ModelConfig {
  switch (contentType) {
    case 'novel':
    case 'long-story':
      return modelConfigs.find(m => m.id === 'gpt-4o') || modelConfigs[0];
    case 'chat':
    case 'quick-generation':
      return modelConfigs.find(m => m.id === 'llama-3.1-8b') || modelConfigs[0];
    case 'analysis':
    case 'academic':
      return modelConfigs.find(m => m.id === 'claude-3-sonnet') || modelConfigs[0];
    default:
      return modelConfigs.find(m => m.id === 'gpt-4o-mini') || modelConfigs[0];
  }
}

// النموذج الافتراضي الموصى به للعربية
export const DEFAULT_MODEL = 'llama-3.3-70b';

// إعدادات النماذج للاستخدام في Supabase Functions
export const supabaseModelConfigs = {
  'mistral-saba-24b': {
    provider: 'groq',
    model: 'mistral-saba-24b',
    maxTokens: 1500,
    temperature: 0.3
  },
  'llama-3.3-70b': {
    provider: 'groq',
    model: 'llama-3.3-70b-versatile',
    maxTokens: 2000,
    temperature: 0.4
  },
  'gpt-4o-mini': {
    provider: 'openai',
    model: 'gpt-4o-mini',
    maxTokens: 1500,
    temperature: 0.3
  },
  'gpt-4o': {
    provider: 'openai',
    model: 'gpt-4o',
    maxTokens: 2000,
    temperature: 0.3
  },
  'llama-3.1-70b': {
    provider: 'groq',
    model: 'llama-3.1-8b-instant',
    maxTokens: 1500,
    temperature: 0.4
  },
  'claude-3-sonnet': {
    provider: 'anthropic',
    model: 'claude-3-sonnet-20240229',
    maxTokens: 1500,
    temperature: 0.8
  }
};
