// اختبار بسيط لـ Groq API
const GROQ_API_KEY = '********************************************************';

async function testGroq() {
  console.log('🔍 اختبار Groq API...');
  
  try {
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${GROQ_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama-3.3-70b-versatile',
        messages: [
          {
            role: 'user',
            content: 'مرحباً، اكتب جملة واحدة بالعربية'
          }
        ],
        max_tokens: 50,
        temperature: 0.7,
      }),
    });

    console.log('📊 حالة الاستجابة:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ خطأ في الاستجابة:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ نجح الاختبار!');
    console.log('📝 الرد:', data.choices[0].message.content);
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error.message);
  }
}

// تشغيل الاختبار
testGroq();
