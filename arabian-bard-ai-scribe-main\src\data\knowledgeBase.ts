// قاعدة المعرفة الأدبية للتدريب المتقدم
export interface LiteraryPassage {
  id: string;
  author: string;
  work: string;
  context: string;
  text: string;
  techniques: string[];
  vocabulary: string[];
  analysis: string[];
  category: 'character_description' | 'place_description' | 'dialogue' | 'narrative' | 'opening' | 'ending';
}

export interface WritingPattern {
  pattern: string;
  examples: string[];
  usage: string;
  frequency: 'high' | 'medium' | 'low';
}

export interface AuthorProfile {
  name: string;
  period: string;
  style: string;
  themes: string[];
  techniques: string[];
  vocabulary: string[];
  patterns: WritingPattern[];
  passages: LiteraryPassage[];
}

// قاعدة المعرفة الأساسية
export const knowledgeBase: Record<string, AuthorProfile> = {
  "نجيب محفوظ": {
    name: "نجيب محفوظ",
    period: "1911-2006",
    style: "الواقعية الاجتماعية",
    themes: [
      "الحياة في الأحياء الشعبية",
      "التغيرات الاجتماعية في مصر",
      "الصراع بين التقاليد والحداثة",
      "الطبقات الاجتماعية",
      "الأسرة المصرية",
      "التطور التاريخي لمصر"
    ],
    techniques: [
      "الوصف التفصيلي للشخصيات والأماكن",
      "التحليل النفسي العميق",
      "استخدام الرمزية الاجتماعية",
      "الحوار المعبر عن الطبقات",
      "التنقل بين الأزمنة",
      "السرد بضمير الغائب مع التدخل أحياناً"
    ],
    vocabulary: [
      "الحارة", "الحوش", "المشربية", "الدكة", "الترام", "القهوة الشعبية",
      "الزقاق", "الحي الشعبي", "الجلباب", "الطربوش", "المقهى الشعبي",
      "الحنين", "الأسى", "الترقب", "الحيرة", "الوقار", "الكرامة"
    ],
    patterns: [
      {
        pattern: "كان {name} رجلاً في {age}، {physical_description}",
        examples: [
          "كان أحمد عبد الجواد رجلاً في الخامسة والأربعين، طويل القامة، عريض المنكبين",
          "كان السيد أحمد رجلاً في الستين، متوسط القامة، أبيض الشعر"
        ],
        usage: "تقديم الشخصيات الرئيسية",
        frequency: "high"
      },
      {
        pattern: "في ذلك الوقت من {time}، كانت {place} تشهد {event}",
        examples: [
          "في ذلك الوقت من المساء، كانت الحارة تشهد حركة نشطة",
          "في تلك الأيام من الصيف، كانت القاهرة تشهد تغيرات كبيرة"
        ],
        usage: "ربط الزمان بالمكان والأحداث",
        frequency: "medium"
      }
    ],
    passages: [
      {
        id: "mahfouz_001",
        author: "نجيب محفوظ",
        work: "بين القصرين",
        context: "وصف الشخصية الرئيسية أحمد عبد الجواد",
        text: "كان أحمد عبد الجواد رجلاً في الخامسة والأربعين، طويل القامة، عريض المنكبين، أسمر اللون، كث اللحية، له عينان سوداوان واسعتان، وأنف أقنى، وفم واسع تبدو منه أسنان بيضاء قوية. وكان يرتدي جلباباً أزرق داكناً وعمامة بيضاء، ويحمل عصا من الخيزران.",
        techniques: [
          "الوصف الجسدي المفصل",
          "تحديد العمر بدقة",
          "وصف الملابس والمظهر",
          "بناء صورة بصرية واضحة"
        ],
        vocabulary: ["طويل القامة", "عريض المنكبين", "كث اللحية", "الجلباب", "العمامة"],
        analysis: [
          "يبدأ بتحديد العمر لإعطاء إطار زمني",
          "يستخدم صفات جسدية محددة وواضحة",
          "يصف الملابس التقليدية للطبقة الوسطى",
          "يخلق صورة مهيبة ووقورة للشخصية"
        ],
        category: "character_description"
      },
      {
        id: "mahfouz_002",
        author: "نجيب محفوظ",
        work: "زقاق المدق",
        context: "وصف الحارة في بداية الرواية",
        text: "كانت الحارة تستيقظ مع أول خيوط الفجر، حين تتسلل أشعة الشمس الذهبية عبر النوافذ الخشبية المتهالكة، لتداعب وجوه النائمين برفق الأم الحانية. وكانت أصوات الباعة تتعالى من الشارع الرئيسي، مختلطة بصهيل الخيول وقرقعة عجلات العربات.",
        techniques: [
          "ربط الوقت بالمكان",
          "استخدام الاستعارة والتشبيه",
          "الوصف الحسي (البصري والسمعي)",
          "إضفاء الحياة على الجماد"
        ],
        vocabulary: ["خيوط الفجر", "أشعة الشمس الذهبية", "النوافذ المتهالكة", "برفق الأم الحانية"],
        analysis: [
          "يربط بين الزمن (الفجر) والمكان (الحارة)",
          "يستخدم الاستعارة (تداعب وجوه النائمين)",
          "يخلق جو حميمي ودافئ",
          "يضيف الأصوات لإكمال الصورة الحسية"
        ],
        category: "place_description"
      }
    ]
  },

  "أحمد مراد": {
    name: "أحمد مراد",
    period: "1978-الآن",
    style: "الإثارة والتشويق المعاصر",
    themes: [
      "الفساد والسلطة",
      "الأسرار والمؤامرات",
      "الصراع النفسي",
      "المجتمع المصري المعاصر",
      "العدالة والانتقام",
      "التكنولوجيا والحداثة"
    ],
    techniques: [
      "البداية المشوقة والجذابة",
      "التشويق المتصاعد",
      "استخدام الفلاش باك",
      "الحوار السريع والحاد",
      "وصف الأجواء المتوترة",
      "الكشف التدريجي للأسرار"
    ],
    vocabulary: [
      "الشارع", "المقهى", "العمارة", "الشقة", "المكتب", "الكافيه",
      "التوتر", "القلق", "الخوف", "الإثارة", "الغضب", "الشك",
      "الفساد", "السلطة", "المؤامرة", "الانتقام", "مشحون", "متوتر"
    ],
    patterns: [
      {
        pattern: "فتح عينيه فجأة. {situation_description}",
        examples: [
          "فتح عينيه فجأة. الظلام يلف المكان، والصمت مطبق",
          "فتح عينيه فجأة. صوت الهاتف يرن بإلحاح في الغرفة المظلمة"
        ],
        usage: "بدايات مشوقة ومثيرة",
        frequency: "high"
      },
      {
        pattern: "لم يكن يتوقع أن {event} سيحدث بهذه السرعة",
        examples: [
          "لم يكن يتوقع أن الأمور ستتطور بهذه السرعة",
          "لم يكن يتوقع أن يكتشف السر بهذه الطريقة"
        ],
        usage: "خلق التوتر والمفاجأة",
        frequency: "medium"
      }
    ],
    passages: [
      {
        id: "murad_001",
        author: "أحمد مراد",
        work: "تراب الماس",
        context: "بداية مشوقة للرواية",
        text: "فتح عينيه فجأة. الظلام يلف المكان، والصمت مطبق إلا من صوت تكييف الهواء الذي يعمل بانتظام. نظر إلى ساعة يده المضيئة: الثالثة والنصف فجراً. شيء ما أيقظه، لكنه لا يستطيع تحديد ماهيته. تسلل إحساس غريب إلى قلبه، إحساس بأن شيئاً ما ليس على ما يرام.",
        techniques: [
          "البداية المباشرة والمشوقة",
          "خلق جو من التوتر والغموض",
          "استخدام التفاصيل الحسية",
          "إثارة فضول القارئ"
        ],
        vocabulary: ["فجأة", "الظلام يلف", "الصمت مطبق", "تسلل إحساس", "ليس على ما يرام"],
        analysis: [
          "يبدأ بفعل مفاجئ يجذب الانتباه",
          "يخلق جو من الغموض والتوتر",
          "يستخدم تفاصيل دقيقة (الوقت، الأصوات)",
          "ينتهي بسؤال ضمني يحفز القراءة"
        ],
        category: "opening"
      }
    ]
  }
};

// دالة البحث في قاعدة المعرفة
export function searchKnowledgeBase(
  author: string,
  category?: string,
  technique?: string
): LiteraryPassage[] {
  const authorData = knowledgeBase[author];
  if (!authorData) return [];

  let results = authorData.passages;

  if (category) {
    results = results.filter(passage => passage.category === category);
  }

  if (technique) {
    results = results.filter(passage => 
      passage.techniques.some(t => t.includes(technique))
    );
  }

  return results;
}

// دالة الحصول على أمثلة للتدريب
export function getTrainingExamples(author: string, count: number = 3): LiteraryPassage[] {
  const authorData = knowledgeBase[author];
  if (!authorData) return [];

  return authorData.passages.slice(0, count);
}

// دالة الحصول على أنماط الكتابة
export function getWritingPatterns(author: string): WritingPattern[] {
  const authorData = knowledgeBase[author];
  if (!authorData) return [];

  return authorData.patterns;
}

// دالة الحصول على المفردات المميزة
export function getAuthorVocabulary(author: string): string[] {
  const authorData = knowledgeBase[author];
  if (!authorData) return [];

  return authorData.vocabulary;
}
